# Database & Backend Improvements

## 1. Enhanced Database Schema

### Missing Tables to Add:
```sql
-- User follows/subscriptions
CREATE TABLE user_follows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  follower_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  following_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(follower_id, following_id)
);

-- Question bookmarks/saves
CREATE TABLE question_bookmarks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, question_id)
);

-- Notifications system
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL, -- 'answer', 'vote', 'follow', 'mention'
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Question views/analytics
CREATE TABLE question_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Moderation/reporting
CREATE TABLE reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  reporter_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  answer_id UUID REFERENCES answers(id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'pending', -- 'pending', 'reviewed', 'resolved'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 2. Database Functions & Triggers

### Auto-update answer counts:
```sql
CREATE OR REPLACE FUNCTION update_question_answer_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE questions 
    SET answer_count = answer_count + 1 
    WHERE id = NEW.question_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE questions 
    SET answer_count = answer_count - 1 
    WHERE id = OLD.question_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_answer_count
  AFTER INSERT OR DELETE ON answers
  FOR EACH ROW EXECUTE FUNCTION update_question_answer_count();
```

## 3. Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE answers ENABLE ROW LEVEL SECURITY;

-- Profiles: Users can read all, update own
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Questions: Public read, authenticated create/update own
CREATE POLICY "Questions are viewable by everyone" ON questions
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create questions" ON questions
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update own questions" ON questions
  FOR UPDATE USING (auth.uid() = author_id);
```

## 4. Database Indexes for Performance

```sql
-- Search and filtering indexes
CREATE INDEX idx_questions_category_created ON questions(category_id, created_at DESC);
CREATE INDEX idx_questions_upvotes ON questions(upvotes DESC);
CREATE INDEX idx_questions_trending ON questions((upvotes - downvotes) DESC, created_at DESC);
CREATE INDEX idx_questions_fulltext ON questions USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- User activity indexes
CREATE INDEX idx_answers_author_created ON answers(author_id, created_at DESC);
CREATE INDEX idx_votes_user_created ON votes(user_id, created_at DESC);
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, read, created_at DESC);
```

## 5. API Routes Structure

Create these API routes in `src/app/api/`:

### Questions API:
- `GET /api/questions` - List questions with filtering
- `POST /api/questions` - Create new question
- `GET /api/questions/[id]` - Get single question
- `PUT /api/questions/[id]` - Update question
- `DELETE /api/questions/[id]` - Delete question

### Answers API:
- `GET /api/questions/[id]/answers` - Get answers for question
- `POST /api/questions/[id]/answers` - Create answer
- `PUT /api/answers/[id]` - Update answer
- `DELETE /api/answers/[id]` - Delete answer

### Voting API:
- `POST /api/questions/[id]/vote` - Vote on question
- `POST /api/answers/[id]/vote` - Vote on answer

### User API:
- `GET /api/users/[id]` - Get user profile
- `PUT /api/users/[id]` - Update profile
- `GET /api/users/[id]/questions` - User's questions
- `GET /api/users/[id]/answers` - User's answers
