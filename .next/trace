[{"name": "hot-reloader", "duration": 114, "timestamp": 18345201342, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1750168219369, "traceId": "79d396e5f8555445"}, {"name": "setup-dev-bundler", "duration": 3334318, "timestamp": 18343756007, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750168217924, "traceId": "79d396e5f8555445"}, {"name": "run-instrumentation-hook", "duration": 36, "timestamp": 18347534130, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750168221702, "traceId": "79d396e5f8555445"}, {"name": "start-dev-server", "duration": 6523647, "timestamp": 18341053413, "id": 1, "tags": {"cpus": "4", "platform": "darwin", "memory.freeMem": "33202176", "memory.totalMem": "8589934592", "memory.heapSizeLimit": "4496293888", "memory.rss": "203177984", "memory.heapTotal": "134303744", "memory.heapUsed": "75302056"}, "startTime": 1750168215222, "traceId": "79d396e5f8555445"}, {"name": "compile-path", "duration": 36415617, "timestamp": 18377620627, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750168251788, "traceId": "79d396e5f8555445"}, {"name": "ensure-page", "duration": 36418416, "timestamp": 18377619587, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750168251787, "traceId": "79d396e5f8555445"}]