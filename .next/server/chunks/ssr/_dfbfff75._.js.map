{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Library/CloudStorage/OneDrive-Personal/mywebmaster/startups/whatz-it/app/wihatz-Cursor-Vercel-v1/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { \n  ChevronUpIcon,\n  ChevronDownIcon,\n  ChatBubbleLeftRightIcon,\n  ArrowTrendingUpIcon,\n  UserGroupIcon,\n  SparklesIcon,\n  FireIcon,\n  ClockIcon,\n  StarIcon,\n  ShareIcon\n} from '@heroicons/react/24/outline'\n\n// Mock data - in real app this would come from database\nconst feedQuestions = [\n  {\n    id: '1',\n    title: 'What is it like to work remotely from different countries?',\n    category: 'Career',\n    categoryColor: 'text-blue-600',\n    author: 'digitalnomad_jane',\n    comments: 24,\n    upvotes: 156,\n    downvotes: 12,\n    timeAgo: '2h',\n    excerpt: 'The freedom is incredible but tax implications can be complex. I&apos;ve been working remotely while traveling for 3 years now, and here&apos;s what I&apos;ve learned...',\n    tags: ['remote-work', 'travel', 'taxes', 'lifestyle']\n  },\n  {\n    id: '2',\n    title: 'What is it like to have ADHD as an adult?',\n    category: 'Health',\n    categoryColor: 'text-green-600',\n    author: 'mindfulbrain',\n    comments: 89,\n    upvotes: 432,\n    downvotes: 8,\n    timeAgo: '4h',\n    excerpt: 'It&apos;s like having a browser with 47 tabs open, and 3 of them are playing music. Getting diagnosed at 28 changed everything for me...',\n    tags: ['adhd', 'mental-health', 'adult-diagnosis', 'medication']\n  },\n  {\n    id: '3',\n    title: 'What is it like to live in Iceland?',\n    category: 'Countries',\n    categoryColor: 'text-purple-600',\n    author: 'reykjavik_resident',\n    comments: 67,\n    upvotes: 289,\n    downvotes: 15,\n    timeAgo: '6h',\n    excerpt: 'The natural beauty is unmatched, but groceries will shock you. Housing is expensive but the social safety net is incredible...',\n    tags: ['iceland', 'cost-of-living', 'culture', 'weather']\n  },\n  {\n    id: '4',\n    title: 'What is it like to quit your corporate job to start freelancing?',\n    category: 'Career',\n    categoryColor: 'text-blue-600',\n    author: 'former_corporate',\n    comments: 43,\n    upvotes: 178,\n    downvotes: 22,\n    timeAgo: '8h',\n    excerpt: 'Terrifying and liberating at the same time. The financial uncertainty is real, but so is the freedom to choose your projects...',\n    tags: ['freelancing', 'career-change', 'entrepreneurship', 'financial-planning']\n  },\n  {\n    id: '5',\n    title: 'What is it like to be a single parent?',\n    category: 'Relationships',\n    categoryColor: 'text-pink-600',\n    author: 'solo_parent_journey',\n    comments: 156,\n    upvotes: 523,\n    downvotes: 7,\n    timeAgo: '12h',\n    excerpt: 'Exhausting but rewarding. You become incredibly resourceful and your relationship with your kids is unique...',\n    tags: ['parenting', 'single-parent', 'work-life-balance', 'support']\n  }\n]\n\nconst popularCommunities = [\n  { name: 'Career', members: '1.2M', color: 'bg-blue-500', trending: true },\n  { name: 'Countries', members: '892K', color: 'bg-purple-500', trending: false },\n  { name: 'Health', members: '756K', color: 'bg-green-500', trending: true },\n  { name: 'Lifestyle', members: '643K', color: 'bg-orange-500', trending: false },\n  { name: 'Education', members: '521K', color: 'bg-indigo-500', trending: false },\n  { name: 'Relationships', members: '489K', color: 'bg-pink-500', trending: true },\n  { name: 'Technology', members: '387K', color: 'bg-gray-500', trending: false },\n  { name: 'Finance', members: '298K', color: 'bg-yellow-500', trending: false }\n]\n\nconst trendingTopics = [\n  { tag: 'remote-work', questions: 234 },\n  { tag: 'mental-health', questions: 189 },\n  { tag: 'career-change', questions: 156 },\n  { tag: 'iceland', questions: 89 },\n  { tag: 'freelancing', questions: 67 }\n]\n\nconst stats = [\n  { name: 'Questions Today', value: '1,247', change: '+12%' },\n  { name: 'Active Members', value: '48.3K', change: '+5%' },\n  { name: 'Expert Contributors', value: '2,847', change: '+8%' },\n]\n\nexport default function Home() {\n  return (\n    <div className=\"flex gap-4 lg:gap-6 max-w-7xl mx-auto\">\n      {/* Left Sidebar */}\n      <div className=\"hidden lg:block w-80 flex-shrink-0\">\n        <div className=\"sticky top-6 space-y-4\">\n          {/* Popular Communities */}\n          <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n            <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n              <h3 className=\"text-sm font-semibold text-gray-900 flex items-center gap-2\">\n                <FireIcon className=\"h-4 w-4 text-orange-500\" />\n                Popular Communities\n              </h3>\n            </div>\n            <div className=\"p-2\">\n              {popularCommunities.slice(0, 6).map((community, index) => (\n                <Link\n                  key={community.name}\n                  href={`/communities/${community.name.toLowerCase()}`}\n                  className=\"flex items-center gap-3 p-2 rounded-md hover:bg-gray-50 group\"\n                >\n                  <span className=\"text-sm font-medium text-gray-600\">{index + 1}</span>\n                  <div className={`w-3 h-3 rounded-full ${community.color}`}></div>\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-sm font-medium text-gray-900 group-hover:text-blue-600\">\n                        {community.name}\n                      </span>\n                      {community.trending && (\n                        <span className=\"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800\">\n                          Hot\n                        </span>\n                      )}\n                    </div>\n                    <span className=\"text-xs text-gray-500\">{community.members} members</span>\n                  </div>\n                </Link>\n              ))}\n            </div>\n            <div className=\"px-4 py-2 border-t border-gray-200\">\n              <Link\n                href=\"/communities\"\n                className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\"\n              >\n                View all communities\n              </Link>\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n            <div className=\"space-y-3\">\n              <Link\n                href=\"/ask\"\n                className=\"flex items-center gap-3 p-3 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors\"\n              >\n                <SparklesIcon className=\"h-5 w-5\" />\n                <span className=\"font-medium\">Ask a Question</span>\n              </Link>\n              <Link\n                href=\"/trending\"\n                className=\"flex items-center gap-3 p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors\"\n              >\n                <ArrowTrendingUpIcon className=\"h-5 w-5 text-orange-500\" />\n                <span className=\"font-medium text-gray-900\">Trending Now</span>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Feed */}\n      <div className=\"flex-1 max-w-2xl\">\n        {/* Feed Header */}\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4 mb-4\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-xl font-bold text-gray-900\">Popular Questions</h1>\n            <div className=\"flex items-center gap-2\">\n              <button className=\"px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 rounded-md\">\n                Hot\n              </button>\n              <button className=\"px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-md\">\n                New\n              </button>\n              <button className=\"px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-md\">\n                Top\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Questions Feed */}\n        <div className=\"space-y-4\">\n          {feedQuestions.map((question) => (\n            <div\n              key={question.id}\n              className=\"bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\"\n            >\n              <div className=\"flex\">\n                {/* Voting Column */}\n                <div className=\"flex flex-col items-center p-2 bg-gray-50 rounded-l-lg\">\n                  <button \n                    className=\"p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-orange-500\"\n                    aria-label=\"Upvote question\"\n                  >\n                    <ChevronUpIcon className=\"h-5 w-5\" />\n                  </button>\n                  <span className=\"text-sm font-semibold text-gray-900 py-1\">\n                    {question.upvotes - question.downvotes}\n                  </span>\n                  <button \n                    className=\"p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-blue-500\"\n                    aria-label=\"Downvote question\"\n                  >\n                    <ChevronDownIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                {/* Content */}\n                <div className=\"flex-1 p-4\">\n                  {/* Header */}\n                  <div className=\"flex items-center gap-2 text-xs text-gray-500 mb-2\">\n                    <Link href={`/communities/${question.category.toLowerCase()}`} className={`font-medium ${question.categoryColor} hover:underline`}>\n                      {question.category}\n                    </Link>\n                    <span>•</span>\n                    <span>Posted by {question.author}</span>\n                    <span>•</span>\n                    <span className=\"flex items-center gap-1\">\n                      <ClockIcon className=\"h-3 w-3\" />\n                      {question.timeAgo}\n                    </span>\n                  </div>\n\n                  {/* Title */}\n                  <h2 className=\"text-lg font-semibold text-gray-900 mb-2 hover:text-blue-600\">\n                    <Link href={`/questions/${question.id}`}>\n                      {question.title}\n                    </Link>\n                  </h2>\n\n                  {/* Excerpt */}\n                  <p className=\"text-gray-700 mb-3 line-clamp-2\">\n                    {question.excerpt}\n                  </p>\n\n                  {/* Tags */}\n                  <div className=\"flex flex-wrap gap-1 mb-3\">\n                    {question.tags.map((tag) => (\n                      <Link\n                        key={tag}\n                        href={`/tags/${tag}`}\n                        className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200\"\n                      >\n                        #{tag}\n                      </Link>\n                    ))}\n                  </div>\n\n                  {/* Footer */}\n                  <div className=\"flex items-center gap-4 text-sm text-gray-500\">\n                    <Link\n                      href={`/questions/${question.id}#answers`}\n                      className=\"flex items-center gap-1 hover:text-gray-700\"\n                    >\n                      <ChatBubbleLeftRightIcon className=\"h-4 w-4\" />\n                      {question.comments} comments\n                    </Link>\n                    <button className=\"flex items-center gap-1 hover:text-gray-700\">\n                      <StarIcon className=\"h-4 w-4\" />\n                      Save\n                    </button>\n                    <button className=\"flex items-center gap-1 hover:text-gray-700\">\n                      <ShareIcon className=\"h-4 w-4\" />\n                      Share\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Load More */}\n        <div className=\"mt-6 text-center\">\n          <button className=\"px-6 py-2 bg-white border border-gray-200 rounded-md text-gray-700 hover:bg-gray-50 font-medium\">\n            Load more questions\n          </button>\n        </div>\n      </div>\n\n      {/* Right Sidebar */}\n      <div className=\"hidden xl:block w-80 flex-shrink-0\">\n        <div className=\"sticky top-6 space-y-4\">\n          {/* Welcome Banner */}\n          <div className=\"bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg p-6 text-white\">\n            <h3 className=\"text-lg font-bold mb-2\">Welcome to Whatsitlike.to</h3>\n            <p className=\"text-sm text-blue-100 mb-4\">\n              Share real experiences and get authentic insights from people who&apos;ve been there.\n            </p>\n            <Link\n              href=\"/ask\"\n              className=\"inline-flex items-center px-4 py-2 bg-white text-blue-600 rounded-md text-sm font-medium hover:bg-gray-50\"\n            >\n              Ask Your First Question\n            </Link>\n          </div>\n\n          {/* Today's Stats */}\n          <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n            <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n              <h3 className=\"text-sm font-semibold text-gray-900\">Today&apos;s Activity</h3>\n            </div>\n            <div className=\"p-4 space-y-3\">\n              {stats.map((stat) => (\n                <div key={stat.name} className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">{stat.name}</p>\n                    <p className=\"text-lg font-semibold text-gray-900\">{stat.value}</p>\n                  </div>\n                  <span className=\"text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded\">\n                    {stat.change}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Trending Topics */}\n          <div className=\"bg-white rounded-lg border border-gray-200 overflow-hidden\">\n            <div className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\">\n              <h3 className=\"text-sm font-semibold text-gray-900 flex items-center gap-2\">\n                <ArrowTrendingUpIcon className=\"h-4 w-4 text-orange-500\" />\n                Trending Topics\n              </h3>\n            </div>\n            <div className=\"p-2\">\n              {trendingTopics.map((topic, index) => (\n                <Link\n                  key={topic.tag}\n                  href={`/tags/${topic.tag}`}\n                  className=\"flex items-center justify-between p-2 rounded-md hover:bg-gray-50 group\"\n                >\n                  <div className=\"flex items-center gap-3\">\n                    <span className=\"text-sm font-medium text-gray-600\">{index + 1}</span>\n                    <span className=\"text-sm font-medium text-gray-900 group-hover:text-blue-600\">\n                      #{topic.tag}\n                    </span>\n                  </div>\n                  <span className=\"text-xs text-gray-500\">{topic.questions} questions</span>\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Rules/Guidelines */}\n          <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n            <h3 className=\"text-sm font-semibold text-gray-900 mb-3\">Community Guidelines</h3>\n            <ul className=\"text-xs text-gray-600 space-y-1\">\n              <li>• Be respectful and kind to others</li>\n              <li>• Share authentic experiences only</li>\n              <li>• No spam or self-promotion</li>\n              <li>• Protect privacy and anonymity</li>\n              <li>• Use appropriate content warnings</li>\n            </ul>\n            <Link\n              href=\"/guidelines\"\n              className=\"text-xs text-blue-600 hover:text-blue-700 font-medium mt-2 inline-block\"\n            >\n              Read full guidelines →\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAaA,wDAAwD;AACxD,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,eAAe;QACf,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,MAAM;YAAC;YAAe;YAAU;YAAS;SAAY;IACvD;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,eAAe;QACf,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,MAAM;YAAC;YAAQ;YAAiB;YAAmB;SAAa;IAClE;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,eAAe;QACf,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,MAAM;YAAC;YAAW;YAAkB;YAAW;SAAU;IAC3D;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,eAAe;QACf,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,MAAM;YAAC;YAAe;YAAiB;YAAoB;SAAqB;IAClF;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,eAAe;QACf,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,MAAM;YAAC;YAAa;YAAiB;YAAqB;SAAU;IACtE;CACD;AAED,MAAM,qBAAqB;IACzB;QAAE,MAAM;QAAU,SAAS;QAAQ,OAAO;QAAe,UAAU;IAAK;IACxE;QAAE,MAAM;QAAa,SAAS;QAAQ,OAAO;QAAiB,UAAU;IAAM;IAC9E;QAAE,MAAM;QAAU,SAAS;QAAQ,OAAO;QAAgB,UAAU;IAAK;IACzE;QAAE,MAAM;QAAa,SAAS;QAAQ,OAAO;QAAiB,UAAU;IAAM;IAC9E;QAAE,MAAM;QAAa,SAAS;QAAQ,OAAO;QAAiB,UAAU;IAAM;IAC9E;QAAE,MAAM;QAAiB,SAAS;QAAQ,OAAO;QAAe,UAAU;IAAK;IAC/E;QAAE,MAAM;QAAc,SAAS;QAAQ,OAAO;QAAe,UAAU;IAAM;IAC7E;QAAE,MAAM;QAAW,SAAS;QAAQ,OAAO;QAAiB,UAAU;IAAM;CAC7E;AAED,MAAM,iBAAiB;IACrB;QAAE,KAAK;QAAe,WAAW;IAAI;IACrC;QAAE,KAAK;QAAiB,WAAW;IAAI;IACvC;QAAE,KAAK;QAAiB,WAAW;IAAI;IACvC;QAAE,KAAK;QAAW,WAAW;IAAG;IAChC;QAAE,KAAK;QAAe,WAAW;IAAG;CACrC;AAED,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAmB,OAAO;QAAS,QAAQ;IAAO;IAC1D;QAAE,MAAM;QAAkB,OAAO;QAAS,QAAQ;IAAM;IACxD;QAAE,MAAM;QAAuB,OAAO;QAAS,QAAQ;IAAM;CAC9D;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA4B;;;;;;;;;;;;8CAIpD,8OAAC;oCAAI,WAAU;8CACZ,mBAAmB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,WAAW,sBAC9C,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,CAAC,aAAa,EAAE,UAAU,IAAI,CAAC,WAAW,IAAI;4CACpD,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAqC,QAAQ;;;;;;8DAC7D,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,UAAU,KAAK,EAAE;;;;;;8DACzD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,UAAU,IAAI;;;;;;gEAEhB,UAAU,QAAQ,kBACjB,8OAAC;oEAAK,WAAU;8EAAmG;;;;;;;;;;;;sEAKvH,8OAAC;4DAAK,WAAU;;gEAAyB,UAAU,OAAO;gEAAC;;;;;;;;;;;;;;2CAjBxD,UAAU,IAAI;;;;;;;;;;8CAsBzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;0DAC/B,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAAsE;;;;;;sDAGxF,8OAAC;4CAAO,WAAU;sDAA4E;;;;;;sDAG9F,8OAAC;4CAAO,WAAU;sDAA4E;;;;;;;;;;;;;;;;;;;;;;;kCAQpG,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,yBAClB,8OAAC;gCAEC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,cAAW;8DAEX,cAAA,8OAAC,yNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,8OAAC;oDAAK,WAAU;8DACb,SAAS,OAAO,GAAG,SAAS,SAAS;;;;;;8DAExC,8OAAC;oDACC,WAAU;oDACV,cAAW;8DAEX,cAAA,8OAAC,6NAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAK/B,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,aAAa,EAAE,SAAS,QAAQ,CAAC,WAAW,IAAI;4DAAE,WAAW,CAAC,YAAY,EAAE,SAAS,aAAa,CAAC,gBAAgB,CAAC;sEAC9H,SAAS,QAAQ;;;;;;sEAEpB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;;gEAAK;gEAAW,SAAS,MAAM;;;;;;;sEAChC,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,iNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEACpB,SAAS,OAAO;;;;;;;;;;;;;8DAKrB,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;kEACpC,SAAS,KAAK;;;;;;;;;;;8DAKnB,8OAAC;oDAAE,WAAU;8DACV,SAAS,OAAO;;;;;;8DAInB,8OAAC;oDAAI,WAAU;8DACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,8OAAC,4JAAA,CAAA,UAAI;4DAEH,MAAM,CAAC,MAAM,EAAE,KAAK;4DACpB,WAAU;;gEACX;gEACG;;2DAJG;;;;;;;;;;8DAUX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,CAAC,QAAQ,CAAC;4DACzC,WAAU;;8EAEV,8OAAC,6OAAA,CAAA,0BAAuB;oEAAC,WAAU;;;;;;gEAClC,SAAS,QAAQ;gEAAC;;;;;;;sEAErB,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC,+MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAGlC,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC,iNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;+BA9EpC,SAAS,EAAE;;;;;;;;;;kCAyFtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,WAAU;sCAAkG;;;;;;;;;;;;;;;;;0BAOxH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAEtD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAyB,KAAK,IAAI;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;sEAAuC,KAAK,KAAK;;;;;;;;;;;;8DAEhE,8OAAC;oDAAK,WAAU;8DACb,KAAK,MAAM;;;;;;;2CANN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAczB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;4CAA4B;;;;;;;;;;;;8CAI/D,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE;4CAC1B,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAqC,QAAQ;;;;;;sEAC7D,8OAAC;4DAAK,WAAU;;gEAA8D;gEAC1E,MAAM,GAAG;;;;;;;;;;;;;8DAGf,8OAAC;oDAAK,WAAU;;wDAAyB,MAAM,SAAS;wDAAC;;;;;;;;2CAVpD,MAAM,GAAG;;;;;;;;;;;;;;;;sCAiBtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAEN,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}