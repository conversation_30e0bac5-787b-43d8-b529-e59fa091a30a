{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Library/CloudStorage/OneDrive-Personal/mywebmaster/startups/whatz-it/app/wihatz-Cursor-Vercel-v1/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n\n// For server-side usage\nexport const createServerSupabaseClient = () => {\n  return createClient<Database>(supabaseUrl, supabaseAnonKey)\n} "], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,cAAc,QAAQ,GAAG,CAAC,wBAAwB,IAAI;AAC5D,MAAM,kBAAkB,QAAQ,GAAG,CAAC,6BAA6B,IAAI;AAE9D,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAGrD,MAAM,6BAA6B;IACxC,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAC7C", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Library/CloudStorage/OneDrive-Personal/mywebmaster/startups/whatz-it/app/wihatz-Cursor-Vercel-v1/src/components/AuthProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType>({\n  user: null,\n  loading: true,\n  signOut: async () => {},\n})\n\nexport const useAuth = () => {\n  return useContext(AuthContext)\n}\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial user\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n\n    getUser()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    loading,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n} "], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAYA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmB;IACjD,MAAM;IACN,SAAS;IACT,SAAS,WAAa;AACxB;AAEO,MAAM,UAAU;IACrB,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Library/CloudStorage/OneDrive-Personal/mywebmaster/startups/whatz-it/app/wihatz-Cursor-Vercel-v1/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/components/AuthProvider'\nimport { \n  MagnifyingGlassIcon, \n  PlusIcon, \n  UserCircleIcon,\n  BellIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline'\nimport { Menu, Transition } from '@headlessui/react'\nimport { Fragment } from 'react'\n\nexport function Navigation() {\n  const { user, signOut } = useAuth()\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 justify-between\">\n          {/* Logo and main nav */}\n          <div className=\"flex\">\n            <div className=\"flex flex-shrink-0 items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold\">\n                <span className=\"text-blue-600\">Whats</span>\n                <span className=\"text-orange-500\">it</span>\n                <span className=\"text-blue-600\">like.to</span>\n              </Link>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              <Link\n                href=\"/\"\n                className=\"inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-900 border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700\"\n              >\n                Home\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700\"\n              >\n                Categories\n              </Link>\n              <Link\n                href=\"/ama\"\n                className=\"inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700\"\n              >\n                AMA Sessions\n              </Link>\n              <Link\n                href=\"/trending\"\n                className=\"inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:border-gray-300 hover:text-gray-700\"\n              >\n                Trending\n              </Link>\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"flex flex-1 items-center justify-center px-2 lg:ml-6 lg:justify-end\">\n            <div className=\"w-full max-w-lg lg:max-w-xs\">\n              <label htmlFor=\"search\" className=\"sr-only\">\n                Search\n              </label>\n              <div className=\"relative\">\n                <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\n                </div>\n                <input\n                  id=\"search\"\n                  name=\"search\"\n                  className=\"block w-full rounded-md border-0 bg-gray-100 py-1.5 pl-10 pr-3 text-gray-900 placeholder:text-gray-400 focus:bg-white focus:ring-2 focus:ring-blue-500 focus:ring-inset sm:text-sm sm:leading-6\"\n                  placeholder=\"Search experiences...\"\n                  type=\"search\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Right side - Ask Question + User menu */}\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/ask\"\n              className=\"inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600\"\n            >\n              <PlusIcon className=\"-ml-0.5 mr-1.5 h-5 w-5\" aria-hidden=\"true\" />\n              Ask Question\n            </Link>\n\n            {user ? (\n              <div className=\"flex items-center space-x-3\">\n                <button\n                  type=\"button\"\n                  className=\"rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                >\n                  <span className=\"sr-only\">View notifications</span>\n                  <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n\n                <Menu as=\"div\" className=\"relative ml-3\">\n                  <div>\n                    <Menu.Button className=\"flex rounded-full bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800\">\n                      <span className=\"sr-only\">Open user menu</span>\n                      <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n                    </Menu.Button>\n                  </div>\n                  <Transition\n                    as={Fragment}\n                    enter=\"transition ease-out duration-100\"\n                    enterFrom=\"transform opacity-0 scale-95\"\n                    enterTo=\"transform opacity-100 scale-100\"\n                    leave=\"transition ease-in duration-75\"\n                    leaveFrom=\"transform opacity-100 scale-100\"\n                    leaveTo=\"transform opacity-0 scale-95\"\n                  >\n                    <Menu.Items className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\">\n                      <Menu.Item>\n                        {({ active }) => (\n                          <Link\n                            href=\"/profile\"\n                            className={`${active ? 'bg-gray-100' : ''} block px-4 py-2 text-sm text-gray-700`}\n                          >\n                            Your Profile\n                          </Link>\n                        )}\n                      </Menu.Item>\n                      <Menu.Item>\n                        {({ active }) => (\n                          <Link\n                            href=\"/my-questions\"\n                            className={`${active ? 'bg-gray-100' : ''} block px-4 py-2 text-sm text-gray-700`}\n                          >\n                            My Questions\n                          </Link>\n                        )}\n                      </Menu.Item>\n                      <Menu.Item>\n                        {({ active }) => (\n                          <Link\n                            href=\"/my-answers\"\n                            className={`${active ? 'bg-gray-100' : ''} block px-4 py-2 text-sm text-gray-700`}\n                          >\n                            My Answers\n                          </Link>\n                        )}\n                      </Menu.Item>\n                      <Menu.Item>\n                        {({ active }) => (\n                          <button\n                            onClick={signOut}\n                            className={`${active ? 'bg-gray-100' : ''} block w-full px-4 py-2 text-left text-sm text-gray-700`}\n                          >\n                            Sign out\n                          </button>\n                        )}\n                      </Menu.Item>\n                    </Menu.Items>\n                  </Transition>\n                </Menu>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <Link\n                  href=\"/auth/signin\"\n                  className=\"text-sm font-medium text-gray-700 hover:text-gray-900\"\n                >\n                  Sign in\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"inline-flex items-center rounded-md bg-gray-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-800\"\n                >\n                  Sign up\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"flex items-center sm:hidden\">\n              <button\n                type=\"button\"\n                className=\"inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n                aria-controls=\"mobile-menu\"\n                aria-expanded=\"false\"\n                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {mobileMenuOpen ? (\n                  <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                ) : (\n                  <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {mobileMenuOpen && (\n          <div className=\"sm:hidden\" id=\"mobile-menu\">\n            <div className=\"space-y-1 pb-3 pt-2\">\n              <Link\n                href=\"/\"\n                className=\"block border-l-4 border-blue-500 bg-blue-50 py-2 pl-3 pr-4 text-base font-medium text-blue-700\"\n              >\n                Home\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block border-l-4 border-transparent py-2 pl-3 pr-4 text-base font-medium text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800\"\n              >\n                Categories\n              </Link>\n              <Link\n                href=\"/ama\"\n                className=\"block border-l-4 border-transparent py-2 pl-3 pr-4 text-base font-medium text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800\"\n              >\n                AMA Sessions\n              </Link>\n              <Link\n                href=\"/trending\"\n                className=\"block border-l-4 border-transparent py-2 pl-3 pr-4 text-base font-medium text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800\"\n              >\n                Trending\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  )\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAAA;AAbA;;;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAkB;;;;;;0DAClC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAS,WAAU;kDAAU;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;oDAAC,WAAU;oDAAwB,eAAY;;;;;;;;;;;0DAErE,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,WAAU;gDACV,aAAY;gDACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;;sCAOb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,+MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAyB,eAAY;;;;;;wCAAS;;;;;;;gCAInE,qBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;sDAG5C,8OAAC,2KAAA,CAAA,OAAI;4CAAC,IAAG;4CAAM,WAAU;;8DACvB,8OAAC;8DACC,cAAA,8OAAC,2KAAA,CAAA,OAAI,CAAC,MAAM;wDAAC,WAAU;;0EACrB,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC,2NAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAG9B,8OAAC,uLAAA,CAAA,aAAU;oDACT,IAAI,qMAAA,CAAA,WAAQ;oDACZ,OAAM;oDACN,WAAU;oDACV,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAQ;8DAER,cAAA,8OAAC,2KAAA,CAAA,OAAI,CAAC,KAAK;wDAAC,WAAU;;0EACpB,8OAAC,2KAAA,CAAA,OAAI,CAAC,IAAI;0EACP,CAAC,EAAE,MAAM,EAAE,iBACV,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAW,GAAG,SAAS,gBAAgB,GAAG,sCAAsC,CAAC;kFAClF;;;;;;;;;;;0EAKL,8OAAC,2KAAA,CAAA,OAAI,CAAC,IAAI;0EACP,CAAC,EAAE,MAAM,EAAE,iBACV,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAW,GAAG,SAAS,gBAAgB,GAAG,sCAAsC,CAAC;kFAClF;;;;;;;;;;;0EAKL,8OAAC,2KAAA,CAAA,OAAI,CAAC,IAAI;0EACP,CAAC,EAAE,MAAM,EAAE,iBACV,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,WAAW,GAAG,SAAS,gBAAgB,GAAG,sCAAsC,CAAC;kFAClF;;;;;;;;;;;0EAKL,8OAAC,2KAAA,CAAA,OAAI,CAAC,IAAI;0EACP,CAAC,EAAE,MAAM,EAAE,iBACV,8OAAC;wEACC,SAAS;wEACT,WAAW,GAAG,SAAS,gBAAgB,GAAG,uDAAuD,CAAC;kFACnG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAUb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,kBAAkB,CAAC;;0DAElC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,+BACC,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ1D,gCACC,8OAAC;oBAAI,WAAU;oBAAY,IAAG;8BAC5B,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}