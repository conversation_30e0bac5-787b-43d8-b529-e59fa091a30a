module.exports = {

"[project]/node_modules/@headlessui/react/dist/utils/env.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "env": (()=>s)
});
var i = Object.defineProperty;
var d = (t, e, n)=>e in t ? i(t, e, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: n
    }) : t[e] = n;
var r = (t, e, n)=>(d(t, typeof e != "symbol" ? e + "" : e, n), n);
class o {
    constructor(){
        r(this, "current", this.detect());
        r(this, "handoffState", "pending");
        r(this, "currentId", 0);
    }
    set(e) {
        this.current !== e && (this.handoffState = "pending", this.currentId = 0, this.current = e);
    }
    reset() {
        this.set(this.detect());
    }
    nextId() {
        return ++this.currentId;
    }
    get isServer() {
        return this.current === "server";
    }
    get isClient() {
        return this.current === "client";
    }
    detect() {
        return typeof window == "undefined" || typeof document == "undefined" ? "server" : "client";
    }
    handoff() {
        this.handoffState === "pending" && (this.handoffState = "complete");
    }
    get isHandoffComplete() {
        return this.handoffState === "complete";
    }
}
let s = new o;
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/owner.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getOwnerDocument": (()=>o)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/env.js [app-ssr] (ecmascript)");
;
function o(n) {
    var e, r;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["env"].isServer ? null : n ? "ownerDocument" in n ? n.ownerDocument : "current" in n ? (r = (e = n.current) == null ? void 0 : e.ownerDocument) != null ? r : document : null : document;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/micro-task.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "microTask": (()=>t)
});
function t(e) {
    typeof queueMicrotask == "function" ? queueMicrotask(e) : Promise.resolve().then(e).catch((o)=>setTimeout(()=>{
            throw o;
        }));
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "disposables": (()=>o)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$micro$2d$task$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/micro-task.js [app-ssr] (ecmascript)");
;
function o() {
    let s = [], r = {
        addEventListener (e, t, n, i) {
            return e.addEventListener(t, n, i), r.add(()=>e.removeEventListener(t, n, i));
        },
        requestAnimationFrame (...e) {
            let t = requestAnimationFrame(...e);
            return r.add(()=>cancelAnimationFrame(t));
        },
        nextFrame (...e) {
            return r.requestAnimationFrame(()=>r.requestAnimationFrame(...e));
        },
        setTimeout (...e) {
            let t = setTimeout(...e);
            return r.add(()=>clearTimeout(t));
        },
        microTask (...e) {
            let t = {
                current: !0
            };
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$micro$2d$task$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["microTask"])(()=>{
                t.current && e[0]();
            }), r.add(()=>{
                t.current = !1;
            });
        },
        style (e, t, n) {
            let i = e.style.getPropertyValue(t);
            return Object.assign(e.style, {
                [t]: n
            }), this.add(()=>{
                Object.assign(e.style, {
                    [t]: i
                });
            });
        },
        group (e) {
            let t = o();
            return e(t), this.add(()=>t.dispose());
        },
        add (e) {
            return s.includes(e) || s.push(e), ()=>{
                let t = s.indexOf(e);
                if (t >= 0) for (let n of s.splice(t, 1))n();
            };
        },
        dispose () {
            for (let e of s.splice(0))e();
        }
    };
    return r;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-disposables.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDisposables": (()=>p)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
;
;
function p() {
    let [e] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>()=>e.dispose(), [
        e
    ]), e;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useIsoMorphicEffect": (()=>n)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/env.js [app-ssr] (ecmascript)");
;
;
let n = (e, t)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["env"].isServer ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(e, t) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLayoutEffect"])(e, t);
};
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-latest-value.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLatestValue": (()=>s)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
;
;
function s(e) {
    let r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(e);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        r.current = e;
    }, [
        e
    ]), r;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useEvent": (()=>o)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-latest-value.js [app-ssr] (ecmascript)");
;
;
let o = function(t) {
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLatestValue"])(t);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useCallback((...r)=>e.current(...r), [
        e
    ]);
};
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-active-press.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useActivePress": (()=>w)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/owner.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
;
;
;
;
function E(e) {
    let t = e.width / 2, n = e.height / 2;
    return {
        top: e.clientY - n,
        right: e.clientX + t,
        bottom: e.clientY + n,
        left: e.clientX - t
    };
}
function P(e, t) {
    return !(!e || !t || e.right < t.left || e.left > t.right || e.bottom < t.top || e.top > t.bottom);
}
function w({ disabled: e = !1 } = {}) {
    let t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), [n, l] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDisposables"])(), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>{
        t.current = null, l(!1), r.dispose();
    }), f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((s)=>{
        if (r.dispose(), t.current === null) {
            t.current = s.currentTarget, l(!0);
            {
                let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getOwnerDocument"])(s.currentTarget);
                r.addEventListener(i, "pointerup", o, !1), r.addEventListener(i, "pointermove", (c)=>{
                    if (t.current) {
                        let p = E(c);
                        l(P(p, t.current.getBoundingClientRect()));
                    }
                }, !1), r.addEventListener(i, "pointercancel", o, !1);
            }
        }
    });
    return {
        pressed: n,
        pressProps: e ? {} : {
            onPointerDown: f,
            onPointerUp: o,
            onClick: o
        }
    };
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-did-element-move.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDidElementMove": (()=>s)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
;
;
function s(n, t) {
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({
        left: 0,
        top: 0
    });
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (!t) return;
        let r = t.getBoundingClientRect();
        r && (e.current = r);
    }, [
        n,
        t
    ]), t == null || !n || t === document.activeElement) return !1;
    let o = t.getBoundingClientRect();
    return o.top !== e.current.top || o.left !== e.current.left;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-element-size.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useElementSize": (()=>d)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
;
;
function f(e) {
    if (e === null) return {
        width: 0,
        height: 0
    };
    let { width: t, height: r } = e.getBoundingClientRect();
    return {
        width: t,
        height: r
    };
}
function d(e, t = !1) {
    let [r, u] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useReducer"])(()=>({}), {}), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>f(e), [
        e,
        r
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (!e) return;
        let n = new ResizeObserver(u);
        return n.observe(e), ()=>{
            n.disconnect();
        };
    }, [
        e
    ]), t ? {
        width: `${i.width}px`,
        height: `${i.height}px`
    } : i;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/default-map.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DefaultMap": (()=>a)
});
class a extends Map {
    constructor(t){
        super();
        this.factory = t;
    }
    get(t) {
        let e = super.get(t);
        return e === void 0 && (e = this.factory(t), this.set(t, e)), e;
    }
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/machine.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Machine": (()=>E),
    "batch": (()=>x),
    "shallowEqual": (()=>j)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$default$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/default-map.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
var p = Object.defineProperty;
var h = (t, e, r)=>e in t ? p(t, e, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
    }) : t[e] = r;
var f = (t, e, r)=>(h(t, typeof e != "symbol" ? e + "" : e, r), r), b = (t, e, r)=>{
    if (!e.has(t)) throw TypeError("Cannot " + r);
};
var n = (t, e, r)=>(b(t, e, "read from private field"), r ? r.call(t) : e.get(t)), c = (t, e, r)=>{
    if (e.has(t)) throw TypeError("Cannot add the same private member more than once");
    e instanceof WeakSet ? e.add(t) : e.set(t, r);
}, u = (t, e, r, s)=>(b(t, e, "write to private field"), s ? s.call(t, r) : e.set(t, r), r);
var i, a, o;
;
;
class E {
    constructor(e){
        c(this, i, {});
        c(this, a, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$default$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DefaultMap"](()=>new Set));
        c(this, o, new Set);
        f(this, "disposables", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])());
        u(this, i, e);
    }
    dispose() {
        this.disposables.dispose();
    }
    get state() {
        return n(this, i);
    }
    subscribe(e, r) {
        let s = {
            selector: e,
            callback: r,
            current: e(n(this, i))
        };
        return n(this, o).add(s), this.disposables.add(()=>{
            n(this, o).delete(s);
        });
    }
    on(e, r) {
        return n(this, a).get(e).add(r), this.disposables.add(()=>{
            n(this, a).get(e).delete(r);
        });
    }
    send(e) {
        let r = this.reduce(n(this, i), e);
        if (r !== n(this, i)) {
            u(this, i, r);
            for (let s of n(this, o)){
                let l = s.selector(n(this, i));
                j(s.current, l) || (s.current = l, s.callback(l));
            }
            for (let s of n(this, a).get(e.type))s(n(this, i), e);
        }
    }
}
i = new WeakMap, a = new WeakMap, o = new WeakMap;
function j(t, e) {
    return Object.is(t, e) ? !0 : typeof t != "object" || t === null || typeof e != "object" || e === null ? !1 : Array.isArray(t) && Array.isArray(e) ? t.length !== e.length ? !1 : d(t[Symbol.iterator](), e[Symbol.iterator]()) : t instanceof Map && e instanceof Map || t instanceof Set && e instanceof Set ? t.size !== e.size ? !1 : d(t.entries(), e.entries()) : y(t) && y(e) ? d(Object.entries(t)[Symbol.iterator](), Object.entries(e)[Symbol.iterator]()) : !1;
}
function d(t, e) {
    do {
        let r = t.next(), s = e.next();
        if (r.done && s.done) return !0;
        if (r.done || s.done || !Object.is(r.value, s.value)) return !1;
    }while (!0)
}
function y(t) {
    if (Object.prototype.toString.call(t) !== "[object Object]") return !1;
    let e = Object.getPrototypeOf(t);
    return e === null || Object.getPrototypeOf(e) === null;
}
function x(t) {
    let [e, r] = t(), s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])();
    return (...l)=>{
        e(...l), s.dispose(), s.microTask(r);
    };
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/match.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "match": (()=>u)
});
function u(r, n, ...a) {
    if (r in n) {
        let e = n[r];
        return typeof e == "function" ? e(...a) : e;
    }
    let t = new Error(`Tried to handle "${r}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map((e)=>`"${e}"`).join(", ")}.`);
    throw Error.captureStackTrace && Error.captureStackTrace(t, u), t;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/machines/stack-machine.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ActionTypes": (()=>k),
    "stackMachines": (()=>x)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/machine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$default$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/default-map.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/match.js [app-ssr] (ecmascript)");
var a = Object.defineProperty;
var r = (e, c, t)=>c in e ? a(e, c, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: t
    }) : e[c] = t;
var p = (e, c, t)=>(r(e, typeof c != "symbol" ? c + "" : c, t), t);
;
;
;
var k = ((t)=>(t[t.Push = 0] = "Push", t[t.Pop = 1] = "Pop", t))(k || {});
let y = {
    [0] (e, c) {
        let t = c.id, s = e.stack, i = e.stack.indexOf(t);
        if (i !== -1) {
            let n = e.stack.slice();
            return n.splice(i, 1), n.push(t), s = n, {
                ...e,
                stack: s
            };
        }
        return {
            ...e,
            stack: [
                ...e.stack,
                t
            ]
        };
    },
    [1] (e, c) {
        let t = c.id, s = e.stack.indexOf(t);
        if (s === -1) return e;
        let i = e.stack.slice();
        return i.splice(s, 1), {
            ...e,
            stack: i
        };
    }
};
class o extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Machine"] {
    constructor(){
        super(...arguments);
        p(this, "actions", {
            push: (t)=>this.send({
                    type: 0,
                    id: t
                }),
            pop: (t)=>this.send({
                    type: 1,
                    id: t
                })
        });
        p(this, "selectors", {
            isTop: (t, s)=>t.stack[t.stack.length - 1] === s,
            inStack: (t, s)=>t.stack.includes(s)
        });
    }
    static new() {
        return new o({
            stack: []
        });
    }
    reduce(t, s) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["match"])(s.type, y, t, s);
    }
}
const x = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$default$2d$map$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DefaultMap"](()=>o.new());
;
}}),
"[project]/node_modules/@headlessui/react/dist/react-glue.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useSlice": (()=>S)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$sync$2d$external$2d$store$2f$with$2d$selector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-sync-external-store/with-selector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/machine.js [app-ssr] (ecmascript)");
;
;
;
function S(e, n, r = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallowEqual"]) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$sync$2d$external$2d$store$2f$with$2d$selector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncExternalStoreWithSelector"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((i)=>e.subscribe(s, i)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>e.state), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>e.state), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(n), r);
}
function s(e) {
    return e;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-is-top-layer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useIsTopLayer": (()=>I)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machines$2f$stack$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/machines/stack-machine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/react-glue.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
;
;
;
;
function I(o, s) {
    let t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), r = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machines$2f$stack$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stackMachines"].get(s), [i, c] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(r, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>[
            r.selectors.isTop(e, t),
            r.selectors.inStack(e, t)
        ], [
        r,
        t
    ]));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (o) return r.actions.push(t), ()=>r.actions.pop(t);
    }, [
        r,
        o,
        t
    ]), o ? c ? i : !0 : !1;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-inert-others.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useInertOthers": (()=>y)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/owner.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$is$2d$top$2d$layer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-is-top-layer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
;
;
;
;
let f = new Map, u = new Map;
function h(t) {
    var e;
    let r = (e = u.get(t)) != null ? e : 0;
    return u.set(t, r + 1), r !== 0 ? ()=>m(t) : (f.set(t, {
        "aria-hidden": t.getAttribute("aria-hidden"),
        inert: t.inert
    }), t.setAttribute("aria-hidden", "true"), t.inert = !0, ()=>m(t));
}
function m(t) {
    var i;
    let r = (i = u.get(t)) != null ? i : 1;
    if (r === 1 ? u.delete(t) : u.set(t, r - 1), r !== 1) return;
    let e = f.get(t);
    e && (e["aria-hidden"] === null ? t.removeAttribute("aria-hidden") : t.setAttribute("aria-hidden", e["aria-hidden"]), t.inert = e.inert, f.delete(t));
}
function y(t, { allowed: r, disallowed: e } = {}) {
    let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$is$2d$top$2d$layer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsTopLayer"])(t, "inert-others");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        var d, c;
        if (!i) return;
        let a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])();
        for (let n of (d = e == null ? void 0 : e()) != null ? d : [])n && a.add(h(n));
        let s = (c = r == null ? void 0 : r()) != null ? c : [];
        for (let n of s){
            if (!n) continue;
            let l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getOwnerDocument"])(n);
            if (!l) continue;
            let o = n.parentElement;
            for(; o && o !== l.body;){
                for (let p of o.children)s.some((E)=>p.contains(E)) || a.add(h(p));
                o = o.parentElement;
            }
        }
        return a.dispose;
    }, [
        i,
        r,
        e
    ]);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "hasInlineStyle": (()=>r),
    "isElement": (()=>t),
    "isHTMLElement": (()=>n),
    "isHTMLFieldSetElement": (()=>a),
    "isHTMLIframeElement": (()=>u),
    "isHTMLInputElement": (()=>l),
    "isHTMLLabelElement": (()=>m),
    "isHTMLLegendElement": (()=>E),
    "isHTMLTextAreaElement": (()=>s),
    "isHTMLorSVGElement": (()=>i),
    "isInteractiveElement": (()=>L),
    "isNode": (()=>o)
});
function o(e) {
    return typeof e != "object" || e === null ? !1 : "nodeType" in e;
}
function t(e) {
    return o(e) && "tagName" in e;
}
function n(e) {
    return t(e) && "accessKey" in e;
}
function i(e) {
    return t(e) && "tabIndex" in e;
}
function r(e) {
    return t(e) && "style" in e;
}
function u(e) {
    return n(e) && e.nodeName === "IFRAME";
}
function l(e) {
    return n(e) && e.nodeName === "INPUT";
}
function s(e) {
    return n(e) && e.nodeName === "TEXTAREA";
}
function m(e) {
    return n(e) && e.nodeName === "LABEL";
}
function a(e) {
    return n(e) && e.nodeName === "FIELDSET";
}
function E(e) {
    return n(e) && e.nodeName === "LEGEND";
}
function L(e) {
    return t(e) ? e.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type="hidden"]),label,select,textarea,video[controls]') : !1;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-on-disappear.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useOnDisappear": (()=>p)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-latest-value.js [app-ssr] (ecmascript)");
;
;
;
;
function p(s, n, o) {
    let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLatestValue"])((t)=>{
        let e = t.getBoundingClientRect();
        e.x === 0 && e.y === 0 && e.width === 0 && e.height === 0 && o();
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!s) return;
        let t = n === null ? null : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLElement"])(n) ? n : n.current;
        if (!t) return;
        let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])();
        if (typeof ResizeObserver != "undefined") {
            let r = new ResizeObserver(()=>i.current(t));
            r.observe(t), e.add(()=>r.disconnect());
        }
        if (typeof IntersectionObserver != "undefined") {
            let r = new IntersectionObserver(()=>i.current(t));
            r.observe(t), e.add(()=>r.disconnect());
        }
        return ()=>e.dispose();
    }, [
        n,
        i,
        s
    ]);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/focus-management.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Focus": (()=>T),
    "FocusResult": (()=>y),
    "FocusableMode": (()=>h),
    "focusElement": (()=>I),
    "focusFrom": (()=>j),
    "focusIn": (()=>g),
    "focusableSelector": (()=>f),
    "getAutoFocusableElements": (()=>O),
    "getFocusableElements": (()=>b),
    "isFocusableElement": (()=>A),
    "restoreFocusIfNecessary": (()=>V),
    "sortByDomNode": (()=>P)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/match.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/owner.js [app-ssr] (ecmascript)");
;
;
;
;
let f = [
    "[contentEditable=true]",
    "[tabindex]",
    "a[href]",
    "area[href]",
    "button:not([disabled])",
    "iframe",
    "input:not([disabled])",
    "select:not([disabled])",
    "textarea:not([disabled])"
].map((e)=>`${e}:not([tabindex='-1'])`).join(","), F = [
    "[data-autofocus]"
].map((e)=>`${e}:not([tabindex='-1'])`).join(",");
var T = ((n)=>(n[n.First = 1] = "First", n[n.Previous = 2] = "Previous", n[n.Next = 4] = "Next", n[n.Last = 8] = "Last", n[n.WrapAround = 16] = "WrapAround", n[n.NoScroll = 32] = "NoScroll", n[n.AutoFocus = 64] = "AutoFocus", n))(T || {}), y = ((o)=>(o[o.Error = 0] = "Error", o[o.Overflow = 1] = "Overflow", o[o.Success = 2] = "Success", o[o.Underflow = 3] = "Underflow", o))(y || {}), S = ((t)=>(t[t.Previous = -1] = "Previous", t[t.Next = 1] = "Next", t))(S || {});
function b(e = document.body) {
    return e == null ? [] : Array.from(e.querySelectorAll(f)).sort((r, t)=>Math.sign((r.tabIndex || Number.MAX_SAFE_INTEGER) - (t.tabIndex || Number.MAX_SAFE_INTEGER)));
}
function O(e = document.body) {
    return e == null ? [] : Array.from(e.querySelectorAll(F)).sort((r, t)=>Math.sign((r.tabIndex || Number.MAX_SAFE_INTEGER) - (t.tabIndex || Number.MAX_SAFE_INTEGER)));
}
var h = ((t)=>(t[t.Strict = 0] = "Strict", t[t.Loose = 1] = "Loose", t))(h || {});
function A(e, r = 0) {
    var t;
    return e === ((t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getOwnerDocument"])(e)) == null ? void 0 : t.body) ? !1 : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["match"])(r, {
        [0] () {
            return e.matches(f);
        },
        [1] () {
            let l = e;
            for(; l !== null;){
                if (l.matches(f)) return !0;
                l = l.parentElement;
            }
            return !1;
        }
    });
}
function V(e) {
    let r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getOwnerDocument"])(e);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])().nextFrame(()=>{
        r && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLorSVGElement"])(r.activeElement) && !A(r.activeElement, 0) && I(e);
    });
}
var H = ((t)=>(t[t.Keyboard = 0] = "Keyboard", t[t.Mouse = 1] = "Mouse", t))(H || {});
typeof window != "undefined" && typeof document != "undefined" && (document.addEventListener("keydown", (e)=>{
    e.metaKey || e.altKey || e.ctrlKey || (document.documentElement.dataset.headlessuiFocusVisible = "");
}, !0), document.addEventListener("click", (e)=>{
    e.detail === 1 ? delete document.documentElement.dataset.headlessuiFocusVisible : e.detail === 0 && (document.documentElement.dataset.headlessuiFocusVisible = "");
}, !0));
function I(e) {
    e == null || e.focus({
        preventScroll: !0
    });
}
let w = [
    "textarea",
    "input"
].join(",");
function _(e) {
    var r, t;
    return (t = (r = e == null ? void 0 : e.matches) == null ? void 0 : r.call(e, w)) != null ? t : !1;
}
function P(e, r = (t)=>t) {
    return e.slice().sort((t, l)=>{
        let o = r(t), c = r(l);
        if (o === null || c === null) return 0;
        let u = o.compareDocumentPosition(c);
        return u & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : u & Node.DOCUMENT_POSITION_PRECEDING ? 1 : 0;
    });
}
function j(e, r) {
    return g(b(), r, {
        relativeTo: e
    });
}
function g(e, r, { sorted: t = !0, relativeTo: l = null, skipElements: o = [] } = {}) {
    let c = Array.isArray(e) ? e.length > 0 ? e[0].ownerDocument : document : e.ownerDocument, u = Array.isArray(e) ? t ? P(e) : e : r & 64 ? O(e) : b(e);
    o.length > 0 && u.length > 1 && (u = u.filter((s)=>!o.some((a)=>a != null && "current" in a ? (a == null ? void 0 : a.current) === s : a === s))), l = l != null ? l : c.activeElement;
    let n = (()=>{
        if (r & 5) return 1;
        if (r & 10) return -1;
        throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last");
    })(), x = (()=>{
        if (r & 1) return 0;
        if (r & 2) return Math.max(0, u.indexOf(l)) - 1;
        if (r & 4) return Math.max(0, u.indexOf(l)) + 1;
        if (r & 8) return u.length - 1;
        throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last");
    })(), M = r & 32 ? {
        preventScroll: !0
    } : {}, m = 0, d = u.length, i;
    do {
        if (m >= d || m + d <= 0) return 0;
        let s = x + m;
        if (r & 16) s = (s + d) % d;
        else {
            if (s < 0) return 3;
            if (s >= d) return 1;
        }
        i = u[s], i == null || i.focus(M), m += n;
    }while (i !== c.activeElement)
    return r & 6 && _(i) && i.select(), 2;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/platform.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isAndroid": (()=>i),
    "isIOS": (()=>t),
    "isMobile": (()=>n)
});
function t() {
    return /iPhone/gi.test(window.navigator.platform) || /Mac/gi.test(window.navigator.platform) && window.navigator.maxTouchPoints > 0;
}
function i() {
    return /Android/gi.test(window.navigator.userAgent);
}
function n() {
    return t() || i();
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-document-event.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDocumentEvent": (()=>i)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-latest-value.js [app-ssr] (ecmascript)");
;
;
function i(t, e, o, n) {
    let u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLatestValue"])(o);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!t) return;
        function r(m) {
            u.current(m);
        }
        return document.addEventListener(e, r, n), ()=>document.removeEventListener(e, r, n);
    }, [
        t,
        e,
        n
    ]);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-window-event.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useWindowEvent": (()=>s)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-latest-value.js [app-ssr] (ecmascript)");
;
;
function s(t, e, o, n) {
    let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLatestValue"])(o);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!t) return;
        function r(d) {
            i.current(d);
        }
        return window.addEventListener(e, r, n), ()=>window.removeEventListener(e, r, n);
    }, [
        t,
        e,
        n
    ]);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-outside-click.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useOutsideClick": (()=>k)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/focus-management.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$platform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/platform.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$document$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-document-event.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-latest-value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$window$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-window-event.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const C = 30;
function k(o, f, h) {
    let m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLatestValue"])(h), s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(function(e, c) {
        if (e.defaultPrevented) return;
        let r = c(e);
        if (r === null || !r.getRootNode().contains(r) || !r.isConnected) return;
        let M = function u(n) {
            return typeof n == "function" ? u(n()) : Array.isArray(n) || n instanceof Set ? n : [
                n
            ];
        }(f);
        for (let u of M)if (u !== null && (u.contains(r) || e.composed && e.composedPath().includes(u))) return;
        return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFocusableElement"])(r, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FocusableMode"].Loose) && r.tabIndex !== -1 && e.preventDefault(), m.current(e, r);
    }, [
        m,
        f
    ]), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$document$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDocumentEvent"])(o, "pointerdown", (t)=>{
        var e, c;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$platform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isMobile"])() || (i.current = ((c = (e = t.composedPath) == null ? void 0 : e.call(t)) == null ? void 0 : c[0]) || t.target);
    }, !0), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$document$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDocumentEvent"])(o, "pointerup", (t)=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$platform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isMobile"])() || !i.current) return;
        let e = i.current;
        return i.current = null, s(t, ()=>e);
    }, !0);
    let l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({
        x: 0,
        y: 0
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$document$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDocumentEvent"])(o, "touchstart", (t)=>{
        l.current.x = t.touches[0].clientX, l.current.y = t.touches[0].clientY;
    }, !0), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$document$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDocumentEvent"])(o, "touchend", (t)=>{
        let e = {
            x: t.changedTouches[0].clientX,
            y: t.changedTouches[0].clientY
        };
        if (!(Math.abs(e.x - l.current.x) >= C || Math.abs(e.y - l.current.y) >= C)) return s(t, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLorSVGElement"])(t.target) ? t.target : null);
    }, !0), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$window$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWindowEvent"])(o, "blur", (t)=>s(t, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLIframeElement"])(window.document.activeElement) ? window.document.activeElement : null), !0);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-owner.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useOwnerDocument": (()=>n)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/owner.js [app-ssr] (ecmascript)");
;
;
function n(...e) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getOwnerDocument"])(...e), [
        ...e
    ]);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-quick-release.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Action": (()=>g),
    "useQuickRelease": (()=>k)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$document$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-document-event.js [app-ssr] (ecmascript)");
;
;
;
var m = ((e)=>(e[e.Ignore = 0] = "Ignore", e[e.Select = 1] = "Select", e[e.Close = 2] = "Close", e))(m || {});
const g = {
    Ignore: {
        kind: 0
    },
    Select: (r)=>({
            kind: 1,
            target: r
        }),
    Close: {
        kind: 2
    }
}, E = 200;
function k(r, { trigger: n, action: s, close: e, select: a }) {
    let o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$document$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDocumentEvent"])(r && n !== null, "pointerdown", (t)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNode"])(t == null ? void 0 : t.target) && n != null && n.contains(t.target) && (o.current = new Date);
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$document$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDocumentEvent"])(r && n !== null, "pointerup", (t)=>{
        if (o.current === null || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLorSVGElement"])(t.target)) return;
        let i = s(t), u = new Date().getTime() - o.current.getTime();
        switch(o.current = null, i.kind){
            case 0:
                return;
            case 1:
                {
                    u > E && (a(i.target), e());
                    break;
                }
            case 2:
                {
                    e();
                    break;
                }
        }
    }, {
        capture: !0
    });
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-resolve-button-type.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useResolveButtonType": (()=>e)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function e(t, u) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        var n;
        if (t.type) return t.type;
        let r = (n = t.as) != null ? n : "button";
        if (typeof r == "string" && r.toLowerCase() === "button" || (u == null ? void 0 : u.tagName) === "BUTTON" && !u.hasAttribute("type")) return "button";
    }, [
        t.type,
        t.as,
        u
    ]);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-store.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useStore": (()=>o)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function o(t) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncExternalStore"])(t.subscribe, t.getSnapshot, t.getSnapshot);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/store.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createStore": (()=>a)
});
function a(o, r) {
    let t = o(), n = new Set;
    return {
        getSnapshot () {
            return t;
        },
        subscribe (e) {
            return n.add(e), ()=>n.delete(e);
        },
        dispatch (e, ...s) {
            let i = r[e].call(t, ...s);
            i && (t = i, n.forEach((c)=>c()));
        }
    };
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "adjustScrollbarPadding": (()=>d)
});
function d() {
    let r;
    return {
        before ({ doc: e }) {
            var l;
            let o = e.documentElement, t = (l = e.defaultView) != null ? l : window;
            r = Math.max(0, t.innerWidth - o.clientWidth);
        },
        after ({ doc: e, d: o }) {
            let t = e.documentElement, l = Math.max(0, t.clientWidth - t.offsetWidth), n = Math.max(0, r - l);
            o.style(t, "paddingRight", `${n}px`);
        }
    };
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "handleIOSLocking": (()=>w)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$platform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/platform.js [app-ssr] (ecmascript)");
;
;
;
function w() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$platform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isIOS"])() ? {
        before ({ doc: n, d: l, meta: f }) {
            function i(a) {
                return f.containers.flatMap((r)=>r()).some((r)=>r.contains(a));
            }
            l.microTask(()=>{
                var c;
                if (window.getComputedStyle(n.documentElement).scrollBehavior !== "auto") {
                    let t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])();
                    t.style(n.documentElement, "scrollBehavior", "auto"), l.add(()=>l.microTask(()=>t.dispose()));
                }
                let a = (c = window.scrollY) != null ? c : window.pageYOffset, r = null;
                l.addEventListener(n, "click", (t)=>{
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLorSVGElement"])(t.target)) try {
                        let e = t.target.closest("a");
                        if (!e) return;
                        let { hash: m } = new URL(e.href), s = n.querySelector(m);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLorSVGElement"])(s) && !i(s) && (r = s);
                    } catch  {}
                }, !0), l.addEventListener(n, "touchstart", (t)=>{
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLorSVGElement"])(t.target) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["hasInlineStyle"])(t.target)) if (i(t.target)) {
                        let e = t.target;
                        for(; e.parentElement && i(e.parentElement);)e = e.parentElement;
                        l.style(e, "overscrollBehavior", "contain");
                    } else l.style(t.target, "touchAction", "none");
                }), l.addEventListener(n, "touchmove", (t)=>{
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLorSVGElement"])(t.target)) {
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLInputElement"])(t.target)) return;
                        if (i(t.target)) {
                            let e = t.target;
                            for(; e.parentElement && e.dataset.headlessuiPortal !== "" && !(e.scrollHeight > e.clientHeight || e.scrollWidth > e.clientWidth);)e = e.parentElement;
                            e.dataset.headlessuiPortal === "" && t.preventDefault();
                        } else t.preventDefault();
                    }
                }, {
                    passive: !1
                }), l.add(()=>{
                    var e;
                    let t = (e = window.scrollY) != null ? e : window.pageYOffset;
                    a !== t && window.scrollTo(0, a), r && r.isConnected && (r.scrollIntoView({
                        block: "nearest"
                    }), r = null);
                });
            });
        }
    } : {};
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "preventScroll": (()=>r)
});
function r() {
    return {
        before ({ doc: e, d: o }) {
            o.style(e.documentElement, "overflow", "hidden");
        }
    };
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "overflows": (()=>a)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/store.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$adjust$2d$scrollbar$2d$padding$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$handle$2d$ios$2d$locking$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$prevent$2d$scroll$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js [app-ssr] (ecmascript)");
;
;
;
;
;
function m(e) {
    let n = {};
    for (let t of e)Object.assign(n, t(n));
    return n;
}
let a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createStore"])(()=>new Map, {
    PUSH (e, n) {
        var o;
        let t = (o = this.get(e)) != null ? o : {
            doc: e,
            count: 0,
            d: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])(),
            meta: new Set
        };
        return t.count++, t.meta.add(n), this.set(e, t), this;
    },
    POP (e, n) {
        let t = this.get(e);
        return t && (t.count--, t.meta.delete(n)), this;
    },
    SCROLL_PREVENT ({ doc: e, d: n, meta: t }) {
        let o = {
            doc: e,
            d: n,
            meta: m(t)
        }, c = [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$handle$2d$ios$2d$locking$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["handleIOSLocking"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$adjust$2d$scrollbar$2d$padding$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjustScrollbarPadding"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$prevent$2d$scroll$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["preventScroll"])()
        ];
        c.forEach(({ before: r })=>r == null ? void 0 : r(o)), c.forEach(({ after: r })=>r == null ? void 0 : r(o));
    },
    SCROLL_ALLOW ({ d: e }) {
        e.dispose();
    },
    TEARDOWN ({ doc: e }) {
        this.delete(e);
    }
});
a.subscribe(()=>{
    let e = a.getSnapshot(), n = new Map;
    for (let [t] of e)n.set(t, t.documentElement.style.overflow);
    for (let t of e.values()){
        let o = n.get(t.doc) === "hidden", c = t.count !== 0;
        (c && !o || !c && o) && a.dispatch(t.count > 0 ? "SCROLL_PREVENT" : "SCROLL_ALLOW", t), t.count === 0 && a.dispatch("TEARDOWN", t);
    }
});
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDocumentOverflowLockedEffect": (()=>a)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-store.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$overflow$2d$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js [app-ssr] (ecmascript)");
;
;
;
function a(r, e, n = ()=>({
        containers: []
    })) {
    let f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useStore"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$overflow$2d$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["overflows"]), o = e ? f.get(e) : void 0, i = o ? o.count > 0 : !1;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (!(!e || !r)) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$overflow$2d$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["overflows"].dispatch("PUSH", e, n), ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$overflow$2d$store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["overflows"].dispatch("POP", e, n);
    }, [
        r,
        e
    ]), i;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-scroll-lock.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useScrollLock": (()=>f)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$use$2d$document$2d$overflow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$is$2d$top$2d$layer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-is-top-layer.js [app-ssr] (ecmascript)");
;
;
function f(e, c, n = ()=>[
        document.body
    ]) {
    let r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$is$2d$top$2d$layer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsTopLayer"])(e, "scroll-lock");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$document$2d$overflow$2f$use$2d$document$2d$overflow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDocumentOverflowLockedEffect"])(r, c, (t)=>{
        var o;
        return {
            containers: [
                ...(o = t.containers) != null ? o : [],
                n
            ]
        };
    });
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-sync-refs.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "optionalRef": (()=>T),
    "useSyncRefs": (()=>y)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
;
;
let u = Symbol();
function T(t, n = !0) {
    return Object.assign(t, {
        [u]: n
    });
}
function y(...t) {
    let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(t);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        n.current = t;
    }, [
        t
    ]);
    let c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>{
        for (let o of n.current)o != null && (typeof o == "function" ? o(e) : o.current = e);
    });
    return t.every((e)=>e == null || (e == null ? void 0 : e[u])) ? void 0 : c;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/get-text-value.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getTextValue": (()=>F)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
;
let a = /([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;
function o(e) {
    var l, n;
    let i = (l = e.innerText) != null ? l : "", t = e.cloneNode(!0);
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLElement"])(t)) return i;
    let u = !1;
    for (let f of t.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))f.remove(), u = !0;
    let r = u ? (n = t.innerText) != null ? n : "" : i;
    return a.test(r) && (r = r.replace(a, "")), r;
}
function F(e) {
    let i = e.getAttribute("aria-label");
    if (typeof i == "string") return i.trim();
    let t = e.getAttribute("aria-labelledby");
    if (t) {
        let u = t.split(" ").map((r)=>{
            let l = document.getElementById(r);
            if (l) {
                let n = l.getAttribute("aria-label");
                return typeof n == "string" ? n.trim() : o(l).trim();
            }
            return null;
        }).filter(Boolean);
        if (u.length > 0) return u.join(", ");
    }
    return o(e).trim();
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-text-value.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTextValue": (()=>s)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$get$2d$text$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/get-text-value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
;
;
;
function s(c) {
    let t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(""), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])("");
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>{
        let e = c.current;
        if (!e) return "";
        let u = e.innerText;
        if (t.current === u) return r.current;
        let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$get$2d$text$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTextValue"])(e).trim().toLowerCase();
        return t.current = u, r.current = n, n;
    });
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-tracked-pointer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTrackedPointer": (()=>u)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function t(e) {
    return [
        e.screenX,
        e.screenY
    ];
}
function u() {
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([
        -1,
        -1
    ]);
    return {
        wasMoved (r) {
            let n = t(r);
            return e.current[0] === n[0] && e.current[1] === n[1] ? !1 : (e.current = n, !0);
        },
        update (r) {
            e.current = t(r);
        }
    };
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-flags.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useFlags": (()=>c)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function c(u = 0) {
    let [t, l] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(u), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>l(e), [
        t
    ]), s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>l((a)=>a | e), [
        t
    ]), m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>(t & e) === e, [
        t
    ]), n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>l((a)=>a & ~e), [
        l
    ]), F = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>l((a)=>a ^ e), [
        l
    ]);
    return {
        flags: t,
        setFlag: g,
        addFlag: s,
        hasFlag: m,
        removeFlag: n,
        toggleFlag: F
    };
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-transition.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "transitionDataAttributes": (()=>R),
    "useTransition": (()=>x)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$flags$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-flags.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
var T, b;
;
;
;
;
;
typeof process != "undefined" && typeof globalThis != "undefined" && typeof Element != "undefined" && ((T = process == null ? void 0 : process.env) == null ? void 0 : T["NODE_ENV"]) === "test" && typeof ((b = Element == null ? void 0 : Element.prototype) == null ? void 0 : b.getAnimations) == "undefined" && (Element.prototype.getAnimations = function() {
    return console.warn([
        "Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.",
        "Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.",
        "",
        "Example usage:",
        "```js",
        "import { mockAnimationsApi } from 'jsdom-testing-mocks'",
        "mockAnimationsApi()",
        "```"
    ].join(`
`)), [];
});
var L = ((r)=>(r[r.None = 0] = "None", r[r.Closed = 1] = "Closed", r[r.Enter = 2] = "Enter", r[r.Leave = 4] = "Leave", r))(L || {});
function R(t) {
    let n = {};
    for(let e in t)t[e] === !0 && (n[`data-${e}`] = "");
    return n;
}
function x(t, n, e, i) {
    let [r, o] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(e), { hasFlag: s, addFlag: a, removeFlag: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$flags$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFlags"])(t && r ? 3 : 0), u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(!1), f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(!1), E = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDisposables"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        var d;
        if (t) {
            if (e && o(!0), !n) {
                e && a(3);
                return;
            }
            return (d = i == null ? void 0 : i.start) == null || d.call(i, e), C(n, {
                inFlight: u,
                prepare () {
                    f.current ? f.current = !1 : f.current = u.current, u.current = !0, !f.current && (e ? (a(3), l(4)) : (a(4), l(2)));
                },
                run () {
                    f.current ? e ? (l(3), a(4)) : (l(4), a(3)) : e ? l(1) : a(1);
                },
                done () {
                    var p;
                    f.current && typeof n.getAnimations == "function" && n.getAnimations().length > 0 || (u.current = !1, l(7), e || o(!1), (p = i == null ? void 0 : i.end) == null || p.call(i, e));
                }
            });
        }
    }, [
        t,
        e,
        n,
        E
    ]), t ? [
        r,
        {
            closed: s(1),
            enter: s(2),
            leave: s(4),
            transition: s(2) || s(4)
        }
    ] : [
        e,
        {
            closed: void 0,
            enter: void 0,
            leave: void 0,
            transition: void 0
        }
    ];
}
function C(t, { prepare: n, run: e, done: i, inFlight: r }) {
    let o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])();
    return j(t, {
        prepare: n,
        inFlight: r
    }), o.nextFrame(()=>{
        e(), o.requestAnimationFrame(()=>{
            o.add(M(t, i));
        });
    }), o.dispose;
}
function M(t, n) {
    var o, s;
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])();
    if (!t) return e.dispose;
    let i = !1;
    e.add(()=>{
        i = !0;
    });
    let r = (s = (o = t.getAnimations) == null ? void 0 : o.call(t).filter((a)=>a instanceof CSSTransition)) != null ? s : [];
    return r.length === 0 ? (n(), e.dispose) : (Promise.allSettled(r.map((a)=>a.finished)).then(()=>{
        i || n();
    }), e.dispose);
}
function j(t, { inFlight: n, prepare: e }) {
    if (n != null && n.current) {
        e();
        return;
    }
    let i = t.style.transition;
    t.style.transition = "none", e(), t.offsetHeight, t.style.transition = i;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-tree-walker.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTreeWalker": (()=>F)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/owner.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
;
;
;
function F(c, { container: e, accept: t, walk: r }) {
    let o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(t), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(r);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        o.current = t, l.current = r;
    }, [
        t,
        r
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (!e || !c) return;
        let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getOwnerDocument"])(e);
        if (!n) return;
        let f = o.current, p = l.current, i = Object.assign((m)=>f(m), {
            acceptNode: f
        }), u = n.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, i, !1);
        for(; u.nextNode();)p(u.currentNode);
    }, [
        e,
        c,
        o,
        l
    ]);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/internal/floating.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FloatingProvider": (()=>Ae),
    "useFloatingPanel": (()=>Re),
    "useFloatingPanelProps": (()=>Te),
    "useFloatingReference": (()=>Fe),
    "useFloatingReferenceProps": (()=>be),
    "useResolvedAnchor": (()=>ye)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$dom$2f$dist$2f$floating$2d$ui$2e$dom$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2d$dom$2f$dist$2f$floating$2d$ui$2e$react$2d$dom$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2f$dist$2f$floating$2d$ui$2e$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@floating-ui/react/dist/floating-ui.react.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
let y = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({
    styles: void 0,
    setReference: ()=>{},
    setFloating: ()=>{},
    getReferenceProps: ()=>({}),
    getFloatingProps: ()=>({}),
    slot: {}
});
y.displayName = "FloatingContext";
let $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
$.displayName = "PlacementContext";
function ye(e) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>e ? typeof e == "string" ? {
            to: e
        } : e : null, [
        e
    ]);
}
function Fe() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(y).setReference;
}
function be() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(y).getReferenceProps;
}
function Te() {
    let { getFloatingProps: e, slot: t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(y);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((...n)=>Object.assign({}, e(...n), {
            "data-anchor": t.anchor
        }), [
        e,
        t
    ]);
}
function Re(e = null) {
    e === !1 && (e = null), typeof e == "string" && (e = {
        to: e
    });
    let t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])($), n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>e, [
        JSON.stringify(e, (l, o)=>{
            var u;
            return (u = o == null ? void 0 : o.outerHTML) != null ? u : o;
        })
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        t == null || t(n != null ? n : null);
    }, [
        t,
        n
    ]);
    let r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(y);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>[
            r.setFloating,
            e ? r.styles : {}
        ], [
        r.setFloating,
        e,
        r.styles
    ]);
}
let D = 4;
function Ae({ children: e, enabled: t = !0 }) {
    let [n, r] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), [l, o] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0), u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), [f, s] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    ce(f);
    let i = t && n !== null && f !== null, { to: F = "bottom", gap: E = 0, offset: A = 0, padding: c = 0, inner: h } = ge(n, f), [a, p = "center"] = F.split(" ");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        i && o(0);
    }, [
        i
    ]);
    let { refs: b, floatingStyles: S, context: g } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2f$dist$2f$floating$2d$ui$2e$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useFloating"])({
        open: i,
        placement: a === "selection" ? p === "center" ? "bottom" : `bottom-${p}` : p === "center" ? `${a}` : `${a}-${p}`,
        strategy: "absolute",
        transform: !1,
        middleware: [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2d$dom$2f$dist$2f$floating$2d$ui$2e$react$2d$dom$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["offset"])({
                mainAxis: a === "selection" ? 0 : E,
                crossAxis: A
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2d$dom$2f$dist$2f$floating$2d$ui$2e$react$2d$dom$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["shift"])({
                padding: c
            }),
            a !== "selection" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2d$dom$2f$dist$2f$floating$2d$ui$2e$react$2d$dom$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flip"])({
                padding: c
            }),
            a === "selection" && h ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2f$dist$2f$floating$2d$ui$2e$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["inner"])({
                ...h,
                padding: c,
                overflowRef: u,
                offset: l,
                minItemsVisible: D,
                referenceOverflowThreshold: c,
                onFallbackChange (P) {
                    var L, N;
                    if (!P) return;
                    let d = g.elements.floating;
                    if (!d) return;
                    let M = parseFloat(getComputedStyle(d).scrollPaddingBottom) || 0, I = Math.min(D, d.childElementCount), W = 0, B = 0;
                    for (let m of (N = (L = g.elements.floating) == null ? void 0 : L.childNodes) != null ? N : [])if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLElement"])(m)) {
                        let x = m.offsetTop, k = x + m.clientHeight + M, H = d.scrollTop, U = H + d.clientHeight;
                        if (x >= H && k <= U) I--;
                        else {
                            B = Math.max(0, Math.min(k, U) - Math.max(x, H)), W = m.clientHeight;
                            break;
                        }
                    }
                    I >= 1 && o((m)=>{
                        let x = W * I - B + M;
                        return m >= x ? m : x;
                    });
                }
            }) : null,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2d$dom$2f$dist$2f$floating$2d$ui$2e$react$2d$dom$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["size"])({
                padding: c,
                apply ({ availableWidth: P, availableHeight: d, elements: M }) {
                    Object.assign(M.floating.style, {
                        overflow: "auto",
                        maxWidth: `${P}px`,
                        maxHeight: `min(var(--anchor-max-height, 100vh), ${d}px)`
                    });
                }
            })
        ].filter(Boolean),
        whileElementsMounted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$dom$2f$dist$2f$floating$2d$ui$2e$dom$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["autoUpdate"]
    }), [w = a, V = p] = g.placement.split("-");
    a === "selection" && (w = "selection");
    let G = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            anchor: [
                w,
                V
            ].filter(Boolean).join(" ")
        }), [
        w,
        V
    ]), K = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2f$dist$2f$floating$2d$ui$2e$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useInnerOffset"])(g, {
        overflowRef: u,
        onChange: o
    }), { getReferenceProps: Q, getFloatingProps: X } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$floating$2d$ui$2f$react$2f$dist$2f$floating$2d$ui$2e$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useInteractions"])([
        K
    ]), Y = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((P)=>{
        s(P), b.setFloating(P);
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])($.Provider, {
        value: r
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(y.Provider, {
        value: {
            setFloating: Y,
            setReference: b.setReference,
            styles: S,
            getReferenceProps: Q,
            getFloatingProps: X,
            slot: G
        }
    }, e));
}
function ce(e) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (!e) return;
        let t = new MutationObserver(()=>{
            let n = window.getComputedStyle(e).maxHeight, r = parseFloat(n);
            if (isNaN(r)) return;
            let l = parseInt(n);
            isNaN(l) || r !== l && (e.style.maxHeight = `${Math.ceil(r)}px`);
        });
        return t.observe(e, {
            attributes: !0,
            attributeFilter: [
                "style"
            ]
        }), ()=>{
            t.disconnect();
        };
    }, [
        e
    ]);
}
function ge(e, t) {
    var o, u, f;
    let n = O((o = e == null ? void 0 : e.gap) != null ? o : "var(--anchor-gap, 0)", t), r = O((u = e == null ? void 0 : e.offset) != null ? u : "var(--anchor-offset, 0)", t), l = O((f = e == null ? void 0 : e.padding) != null ? f : "var(--anchor-padding, 0)", t);
    return {
        ...e,
        gap: n,
        offset: r,
        padding: l
    };
}
function O(e, t, n = void 0) {
    let r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDisposables"])(), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((s, i)=>{
        if (s == null) return [
            n,
            null
        ];
        if (typeof s == "number") return [
            s,
            null
        ];
        if (typeof s == "string") {
            if (!i) return [
                n,
                null
            ];
            let F = J(s, i);
            return [
                F,
                (E)=>{
                    let A = q(s);
                    {
                        let c = A.map((h)=>window.getComputedStyle(i).getPropertyValue(h));
                        r.requestAnimationFrame(function h() {
                            r.nextFrame(h);
                            let a = !1;
                            for (let [b, S] of A.entries()){
                                let g = window.getComputedStyle(i).getPropertyValue(S);
                                if (c[b] !== g) {
                                    c[b] = g, a = !0;
                                    break;
                                }
                            }
                            if (!a) return;
                            let p = J(s, i);
                            F !== p && (E(p), F = p);
                        });
                    }
                    return r.dispose;
                }
            ];
        }
        return [
            n,
            null
        ];
    }), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>l(e, t)[0], [
        e,
        t
    ]), [u = o, f] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        let [s, i] = l(e, t);
        if (f(s), !!i) return i(f);
    }, [
        e,
        t
    ]), u;
}
function q(e) {
    let t = /var\((.*)\)/.exec(e);
    if (t) {
        let n = t[1].indexOf(",");
        if (n === -1) return [
            t[1]
        ];
        let r = t[1].slice(0, n).trim(), l = t[1].slice(n + 1).trim();
        return l ? [
            r,
            ...q(l)
        ] : [
            r
        ];
    }
    return [];
}
function J(e, t) {
    let n = document.createElement("div");
    t.appendChild(n), n.style.setProperty("margin-top", "0px", "important"), n.style.setProperty("margin-top", e, "important");
    let r = parseFloat(window.getComputedStyle(n).marginTop) || 0;
    return t.removeChild(n), r;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/internal/open-closed.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OpenClosedProvider": (()=>c),
    "ResetOpenClosedProvider": (()=>s),
    "State": (()=>i),
    "useOpenClosed": (()=>u)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
n.displayName = "OpenClosedContext";
var i = ((e)=>(e[e.Open = 1] = "Open", e[e.Closed = 2] = "Closed", e[e.Closing = 4] = "Closing", e[e.Opening = 8] = "Opening", e))(i || {});
function u() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(n);
}
function c({ value: o, children: t }) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(n.Provider, {
        value: o
    }, t);
}
function s({ children: o }) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(n.Provider, {
        value: null
    }, o);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/bugs.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isDisabledReactIssue7711": (()=>s)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
;
function s(l) {
    let e = l.parentElement, t = null;
    for(; e && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLFieldSetElement"])(e);)(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLLegendElement"])(e) && (t = e), e = e.parentElement;
    let i = (e == null ? void 0 : e.getAttribute("disabled")) === "";
    return i && r(t) ? !1 : i;
}
function r(l) {
    if (!l) return !1;
    let e = l.previousElementSibling;
    for(; e !== null;){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLLegendElement"])(e)) return !1;
        e = e.previousElementSibling;
    }
    return !0;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/calculate-active-index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Focus": (()=>c),
    "calculateActiveIndex": (()=>f)
});
function u(l) {
    throw new Error("Unexpected object: " + l);
}
var c = ((i)=>(i[i.First = 0] = "First", i[i.Previous = 1] = "Previous", i[i.Next = 2] = "Next", i[i.Last = 3] = "Last", i[i.Specific = 4] = "Specific", i[i.Nothing = 5] = "Nothing", i))(c || {});
function f(l, n) {
    let t = n.resolveItems();
    if (t.length <= 0) return null;
    let r = n.resolveActiveIndex(), s = r != null ? r : -1;
    switch(l.focus){
        case 0:
            {
                for(let e = 0; e < t.length; ++e)if (!n.resolveDisabled(t[e], e, t)) return e;
                return r;
            }
        case 1:
            {
                s === -1 && (s = t.length);
                for(let e = s - 1; e >= 0; --e)if (!n.resolveDisabled(t[e], e, t)) return e;
                return r;
            }
        case 2:
            {
                for(let e = s + 1; e < t.length; ++e)if (!n.resolveDisabled(t[e], e, t)) return e;
                return r;
            }
        case 3:
            {
                for(let e = t.length - 1; e >= 0; --e)if (!n.resolveDisabled(t[e], e, t)) return e;
                return r;
            }
        case 4:
            {
                for(let e = 0; e < t.length; ++e)if (n.resolveId(t[e], e, t) === l.id) return e;
                return r;
            }
        case 5:
            return null;
        default:
            u(l);
    }
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/class-names.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "classNames": (()=>t)
});
function t(...r) {
    return Array.from(new Set(r.flatMap((n)=>typeof n == "string" ? n.split(" ") : []))).filter(Boolean).join(" ");
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/utils/render.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RenderFeatures": (()=>O),
    "RenderStrategy": (()=>A),
    "compact": (()=>m),
    "forwardRefWithAs": (()=>K),
    "mergeProps": (()=>_),
    "useRender": (()=>L)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$class$2d$names$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/class-names.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/match.js [app-ssr] (ecmascript)");
;
;
;
var O = ((a)=>(a[a.None = 0] = "None", a[a.RenderStrategy = 1] = "RenderStrategy", a[a.Static = 2] = "Static", a))(O || {}), A = ((e)=>(e[e.Unmount = 0] = "Unmount", e[e.Hidden = 1] = "Hidden", e))(A || {});
function L() {
    let n = U();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((r)=>C({
            mergeRefs: n,
            ...r
        }), [
        n
    ]);
}
function C({ ourProps: n, theirProps: r, slot: e, defaultTag: a, features: s, visible: t = !0, name: l, mergeRefs: i }) {
    i = i != null ? i : $;
    let o = P(r, n);
    if (t) return F(o, e, a, l, i);
    let y = s != null ? s : 0;
    if (y & 2) {
        let { static: f = !1, ...u } = o;
        if (f) return F(u, e, a, l, i);
    }
    if (y & 1) {
        let { unmount: f = !0, ...u } = o;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["match"])(f ? 0 : 1, {
            [0] () {
                return null;
            },
            [1] () {
                return F({
                    ...u,
                    hidden: !0,
                    style: {
                        display: "none"
                    }
                }, e, a, l, i);
            }
        });
    }
    return F(o, e, a, l, i);
}
function F(n, r = {}, e, a, s) {
    let { as: t = e, children: l, refName: i = "ref", ...o } = h(n, [
        "unmount",
        "static"
    ]), y = n.ref !== void 0 ? {
        [i]: n.ref
    } : {}, f = typeof l == "function" ? l(r) : l;
    "className" in o && o.className && typeof o.className == "function" && (o.className = o.className(r)), o["aria-labelledby"] && o["aria-labelledby"] === o.id && (o["aria-labelledby"] = void 0);
    let u = {};
    if (r) {
        let d = !1, p = [];
        for (let [c, T] of Object.entries(r))typeof T == "boolean" && (d = !0), T === !0 && p.push(c.replace(/([A-Z])/g, (g)=>`-${g.toLowerCase()}`));
        if (d) {
            u["data-headlessui-state"] = p.join(" ");
            for (let c of p)u[`data-${c}`] = "";
        }
    }
    if (t === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"] && (Object.keys(m(o)).length > 0 || Object.keys(m(u)).length > 0)) if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValidElement"])(f) || Array.isArray(f) && f.length > 1) {
        if (Object.keys(m(o)).length > 0) throw new Error([
            'Passing props on "Fragment"!',
            "",
            `The current component <${a} /> is rendering a "Fragment".`,
            "However we need to passthrough the following props:",
            Object.keys(m(o)).concat(Object.keys(m(u))).map((d)=>`  - ${d}`).join(`
`),
            "",
            "You can apply a few solutions:",
            [
                'Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',
                "Render a single element as the child so that we can forward the props onto that element."
            ].map((d)=>`  - ${d}`).join(`
`)
        ].join(`
`));
    } else {
        let d = f.props, p = d == null ? void 0 : d.className, c = typeof p == "function" ? (...R)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$class$2d$names$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classNames"])(p(...R), o.className) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$class$2d$names$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classNames"])(p, o.className), T = c ? {
            className: c
        } : {}, g = P(f.props, m(h(o, [
            "ref"
        ])));
        for(let R in u)R in g && delete u[R];
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cloneElement"])(f, Object.assign({}, g, u, y, {
            ref: s(H(f), y.ref)
        }, T));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(t, Object.assign({}, h(o, [
        "ref"
    ]), t !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"] && y, t !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"] && u), f);
}
function U() {
    let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        for (let a of n.current)a != null && (typeof a == "function" ? a(e) : a.current = e);
    }, []);
    return (...e)=>{
        if (!e.every((a)=>a == null)) return n.current = e, r;
    };
}
function $(...n) {
    return n.every((r)=>r == null) ? void 0 : (r)=>{
        for (let e of n)e != null && (typeof e == "function" ? e(r) : e.current = r);
    };
}
function P(...n) {
    var a;
    if (n.length === 0) return {};
    if (n.length === 1) return n[0];
    let r = {}, e = {};
    for (let s of n)for(let t in s)t.startsWith("on") && typeof s[t] == "function" ? ((a = e[t]) != null || (e[t] = []), e[t].push(s[t])) : r[t] = s[t];
    if (r.disabled || r["aria-disabled"]) for(let s in e)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(s) && (e[s] = [
        (t)=>{
            var l;
            return (l = t == null ? void 0 : t.preventDefault) == null ? void 0 : l.call(t);
        }
    ]);
    for(let s in e)Object.assign(r, {
        [s] (t, ...l) {
            let i = e[s];
            for (let o of i){
                if ((t instanceof Event || (t == null ? void 0 : t.nativeEvent) instanceof Event) && t.defaultPrevented) return;
                o(t, ...l);
            }
        }
    });
    return r;
}
function _(...n) {
    var a;
    if (n.length === 0) return {};
    if (n.length === 1) return n[0];
    let r = {}, e = {};
    for (let s of n)for(let t in s)t.startsWith("on") && typeof s[t] == "function" ? ((a = e[t]) != null || (e[t] = []), e[t].push(s[t])) : r[t] = s[t];
    for(let s in e)Object.assign(r, {
        [s] (...t) {
            let l = e[s];
            for (let i of l)i == null || i(...t);
        }
    });
    return r;
}
function K(n) {
    var r;
    return Object.assign((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(n), {
        displayName: (r = n.displayName) != null ? r : n.name
    });
}
function m(n) {
    let r = Object.assign({}, n);
    for(let e in r)r[e] === void 0 && delete r[e];
    return r;
}
function h(n, r = []) {
    let e = Object.assign({}, n);
    for (let a of r)a in e && delete e[a];
    return e;
}
function H(n) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].version.split(".")[0] >= "19" ? n.props.ref : n.ref;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/internal/disabled.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DisabledProvider": (()=>l),
    "useDisabled": (()=>a)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(void 0);
function a() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(e);
}
function l({ value: t, children: o }) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(e.Provider, {
        value: t
    }, o);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/components/description/description.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Description": (()=>H),
    "useDescribedBy": (()=>U),
    "useDescriptions": (()=>w)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-sync-refs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$disabled$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/internal/disabled.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/render.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
let a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
a.displayName = "DescriptionContext";
function f() {
    let r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(a);
    if (r === null) {
        let e = new Error("You used a <Description /> component, but it is not inside a relevant parent.");
        throw Error.captureStackTrace && Error.captureStackTrace(e, f), e;
    }
    return r;
}
function U() {
    var r, e;
    return (e = (r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(a)) == null ? void 0 : r.value) != null ? e : void 0;
}
function w() {
    let [r, e] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    return [
        r.length > 0 ? r.join(" ") : void 0,
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>function(t) {
                let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((n)=>(e((s)=>[
                            ...s,
                            n
                        ]), ()=>e((s)=>{
                            let o = s.slice(), p = o.indexOf(n);
                            return p !== -1 && o.splice(p, 1), o;
                        }))), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
                        register: i,
                        slot: t.slot,
                        name: t.name,
                        props: t.props,
                        value: t.value
                    }), [
                    i,
                    t.slot,
                    t.name,
                    t.props,
                    t.value
                ]);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(a.Provider, {
                    value: l
                }, t.children);
            }, [
            e
        ])
    ];
}
let S = "p";
function C(r, e) {
    let d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$disabled$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDisabled"])(), { id: i = `headlessui-description-${d}`, ...l } = r, n = f(), s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(e);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>n.register(i), [
        i,
        n.register
    ]);
    let o = t || !1, p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            ...n.slot,
            disabled: o
        }), [
        n.slot,
        o
    ]), D = {
        ref: s,
        ...n.props,
        id: i
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])()({
        ourProps: D,
        theirProps: l,
        slot: p,
        defaultTag: S,
        name: n.name || "Description"
    });
}
let _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(C), H = Object.assign(_, {});
;
}}),
"[project]/node_modules/@headlessui/react/dist/components/keyboard.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Keys": (()=>o)
});
var o = ((r)=>(r.Space = " ", r.Enter = "Enter", r.Escape = "Escape", r.Backspace = "Backspace", r.Delete = "Delete", r.ArrowLeft = "ArrowLeft", r.ArrowUp = "ArrowUp", r.ArrowRight = "ArrowRight", r.ArrowDown = "ArrowDown", r.Home = "Home", r.End = "End", r.PageUp = "PageUp", r.PageDown = "PageDown", r.Tab = "Tab", r))(o || {});
;
}}),
"[project]/node_modules/@headlessui/react/dist/internal/id.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "IdProvider": (()=>f),
    "useProvidedId": (()=>u)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(void 0);
function u() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(e);
}
function f({ id: t, children: r }) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(e.Provider, {
        value: t
    }, r);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/components/label/label.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>V),
    "useLabelContext": (()=>C),
    "useLabelledBy": (()=>N),
    "useLabels": (()=>Q)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-sync-refs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$disabled$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/internal/disabled.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$id$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/internal/id.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/render.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
let L = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
L.displayName = "LabelContext";
function C() {
    let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(L);
    if (n === null) {
        let l = new Error("You used a <Label /> component, but it is not inside a relevant parent.");
        throw Error.captureStackTrace && Error.captureStackTrace(l, C), l;
    }
    return n;
}
function N(n) {
    var a, e, o;
    let l = (e = (a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(L)) == null ? void 0 : a.value) != null ? e : void 0;
    return ((o = n == null ? void 0 : n.length) != null ? o : 0) > 0 ? [
        l,
        ...n
    ].filter(Boolean).join(" ") : l;
}
function Q({ inherit: n = !1 } = {}) {
    let l = N(), [a, e] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]), o = n ? [
        l,
        ...a
    ].filter(Boolean) : a;
    return [
        o.length > 0 ? o.join(" ") : void 0,
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>function(t) {
                let p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((i)=>(e((u)=>[
                            ...u,
                            i
                        ]), ()=>e((u)=>{
                            let d = u.slice(), f = d.indexOf(i);
                            return f !== -1 && d.splice(f, 1), d;
                        }))), b = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
                        register: p,
                        slot: t.slot,
                        name: t.name,
                        props: t.props,
                        value: t.value
                    }), [
                    p,
                    t.slot,
                    t.name,
                    t.props,
                    t.value
                ]);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(L.Provider, {
                    value: b
                }, t.children);
            }, [
            e
        ])
    ];
}
let G = "label";
function U(n, l) {
    var E;
    let a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), e = C(), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$id$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useProvidedId"])(), y = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$disabled$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDisabled"])(), { id: t = `headlessui-label-${a}`, htmlFor: p = o != null ? o : (E = e.props) == null ? void 0 : E.htmlFor, passive: b = !1, ...i } = n, u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(l);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>e.register(t), [
        t,
        e.register
    ]);
    let d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((s)=>{
        let g = s.currentTarget;
        if (!(s.target !== s.currentTarget && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isInteractiveElement"])(s.target)) && ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLLabelElement"])(g) && s.preventDefault(), e.props && "onClick" in e.props && typeof e.props.onClick == "function" && e.props.onClick(s), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLLabelElement"])(g))) {
            let r = document.getElementById(g.htmlFor);
            if (r) {
                let x = r.getAttribute("disabled");
                if (x === "true" || x === "") return;
                let h = r.getAttribute("aria-disabled");
                if (h === "true" || h === "") return;
                ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLInputElement"])(r) && (r.type === "file" || r.type === "radio" || r.type === "checkbox") || r.role === "radio" || r.role === "checkbox" || r.role === "switch") && r.click(), r.focus({
                    preventScroll: !0
                });
            }
        }
    }), f = y || !1, R = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            ...e.slot,
            disabled: f
        }), [
        e.slot,
        f
    ]), c = {
        ref: u,
        ...e.props,
        id: t,
        htmlFor: p,
        onClick: d
    };
    return b && ("onClick" in c && (delete c.htmlFor, delete c.onClick), "onClick" in i && delete i.onClick), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])()({
        ourProps: c,
        theirProps: i,
        slot: R,
        defaultTag: p ? G : "div",
        name: e.name || "Label"
    });
}
let j = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(U), V = Object.assign(j, {});
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-on-unmount.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useOnUnmount": (()=>c)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$micro$2d$task$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/micro-task.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
;
;
;
function c(t) {
    let r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(t), e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>(e.current = !1, ()=>{
            e.current = !0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$micro$2d$task$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["microTask"])(()=>{
                e.current && r();
            });
        }), [
        r
    ]);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useServerHandoffComplete": (()=>l)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/env.js [app-ssr] (ecmascript)");
;
;
function s() {
    let r = typeof document == "undefined";
    return "useSyncExternalStore" in __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ ? ((o)=>o.useSyncExternalStore)(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__)(()=>()=>{}, ()=>!1, ()=>!r) : !1;
}
function l() {
    let r = s(), [e, n] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.useState(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["env"].isHandoffComplete);
    return e && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["env"].isHandoffComplete === !1 && n(!1), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.useEffect(()=>{
        e !== !0 && n(!0);
    }, [
        e
    ]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.useEffect(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["env"].handoff(), []), r ? !1 : e;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/internal/portal-force-root.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ForcePortalRoot": (()=>l),
    "usePortalRoot": (()=>a)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(!1);
function a() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(e);
}
function l(o) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(e.Provider, {
        value: o.force
    }, o.children);
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/components/portal/portal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Portal": (()=>ne),
    "PortalGroup": (()=>q),
    "useNestedPortals": (()=>oe)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$on$2d$unmount$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-on-unmount.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-owner.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$server$2d$handoff$2d$complete$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-sync-refs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$portal$2d$force$2d$root$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/internal/portal-force-root.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/env.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/render.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
function I(e) {
    let l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$portal$2d$force$2d$root$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePortalRoot"])(), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(H), [r, u] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>{
        var i;
        if (!l && o !== null) return (i = o.current) != null ? i : null;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["env"].isServer) return null;
        let t = e == null ? void 0 : e.getElementById("headlessui-portal-root");
        if (t) return t;
        if (e === null) return null;
        let a = e.createElement("div");
        return a.setAttribute("id", "headlessui-portal-root"), e.body.appendChild(a);
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        r !== null && (e != null && e.body.contains(r) || e == null || e.body.appendChild(r));
    }, [
        r,
        e
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        l || o !== null && u(o.current);
    }, [
        o,
        u,
        l
    ]), r;
}
let M = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], D = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(function(l, o) {
    let { ownerDocument: r = null, ...u } = l, t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["optionalRef"])((s)=>{
        t.current = s;
    }), o), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOwnerDocument"])(t), f = r != null ? r : i, p = I(f), [n] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>{
        var s;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["env"].isServer ? null : (s = f == null ? void 0 : f.createElement("div")) != null ? s : null;
    }), P = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(g), O = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$server$2d$handoff$2d$complete$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useServerHandoffComplete"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        !p || !n || p.contains(n) || (n.setAttribute("data-headlessui-portal", ""), p.appendChild(n));
    }, [
        p,
        n
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (n && P) return P.register(n);
    }, [
        P,
        n
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$on$2d$unmount$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOnUnmount"])(()=>{
        var s;
        !p || !n || ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNode"])(n) && p.contains(n) && p.removeChild(n), p.childNodes.length <= 0 && ((s = p.parentElement) == null || s.removeChild(p)));
    });
    let b = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return O ? !p || !n ? null : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createPortal"])(b({
        ourProps: {
            ref: a
        },
        theirProps: u,
        slot: {},
        defaultTag: M,
        name: "Portal"
    }), n) : null;
});
function J(e, l) {
    let o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(l), { enabled: r = !0, ownerDocument: u, ...t } = e, a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return r ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(D, {
        ...t,
        ownerDocument: u,
        ref: o
    }) : a({
        ourProps: {
            ref: o
        },
        theirProps: t,
        slot: {},
        defaultTag: M,
        name: "Portal"
    });
}
let X = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], H = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
function k(e, l) {
    let { target: o, ...r } = e, t = {
        ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(l)
    }, a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(H.Provider, {
        value: o
    }, a({
        ourProps: t,
        theirProps: r,
        defaultTag: X,
        name: "Popover.Group"
    }));
}
let g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
function oe() {
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(g), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((t)=>(l.current.push(t), e && e.register(t), ()=>r(t))), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((t)=>{
        let a = l.current.indexOf(t);
        a !== -1 && l.current.splice(a, 1), e && e.unregister(t);
    }), u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            register: o,
            unregister: r,
            portals: l
        }), [
        o,
        r,
        l
    ]);
    return [
        l,
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>function({ children: a }) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(g.Provider, {
                    value: u
                }, a);
            }, [
            u
        ])
    ];
}
let B = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(J), q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(k), ne = Object.assign(B, {
    Group: q
});
;
}}),
"[project]/node_modules/@headlessui/react/dist/components/menu/menu-machine.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ActionTypes": (()=>F),
    "ActivationTrigger": (()=>O),
    "MenuMachine": (()=>x),
    "MenuState": (()=>E)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/machine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machines$2f$stack$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/machines/stack-machine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/calculate-active-index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/focus-management.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/match.js [app-ssr] (ecmascript)");
var h = Object.defineProperty;
var y = (e, i, t)=>i in e ? h(e, i, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: t
    }) : e[i] = t;
var g = (e, i, t)=>(y(e, typeof i != "symbol" ? i + "" : i, t), t);
;
;
;
;
;
var E = ((t)=>(t[t.Open = 0] = "Open", t[t.Closed = 1] = "Closed", t))(E || {}), O = ((t)=>(t[t.Pointer = 0] = "Pointer", t[t.Other = 1] = "Other", t))(O || {}), F = ((r)=>(r[r.OpenMenu = 0] = "OpenMenu", r[r.CloseMenu = 1] = "CloseMenu", r[r.GoToItem = 2] = "GoToItem", r[r.Search = 3] = "Search", r[r.ClearSearch = 4] = "ClearSearch", r[r.RegisterItems = 5] = "RegisterItems", r[r.UnregisterItems = 6] = "UnregisterItems", r[r.SetButtonElement = 7] = "SetButtonElement", r[r.SetItemsElement = 8] = "SetItemsElement", r[r.SortItems = 9] = "SortItems", r))(F || {});
function S(e, i = (t)=>t) {
    let t = e.activeItemIndex !== null ? e.items[e.activeItemIndex] : null, n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sortByDomNode"])(i(e.items.slice()), (l)=>l.dataRef.current.domRef.current), s = t ? n.indexOf(t) : null;
    return s === -1 && (s = null), {
        items: n,
        activeItemIndex: s
    };
}
let D = {
    [1] (e) {
        return e.menuState === 1 ? e : {
            ...e,
            activeItemIndex: null,
            pendingFocus: {
                focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Nothing
            },
            menuState: 1
        };
    },
    [0] (e, i) {
        return e.menuState === 0 ? e : {
            ...e,
            __demoMode: !1,
            pendingFocus: i.focus,
            menuState: 0
        };
    },
    [2]: (e, i)=>{
        var l, o, d, a, I;
        if (e.menuState === 1) return e;
        let t = {
            ...e,
            searchQuery: "",
            activationTrigger: (l = i.trigger) != null ? l : 1,
            __demoMode: !1
        };
        if (i.focus === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Nothing) return {
            ...t,
            activeItemIndex: null
        };
        if (i.focus === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Specific) return {
            ...t,
            activeItemIndex: e.items.findIndex((r)=>r.id === i.id)
        };
        if (i.focus === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Previous) {
            let r = e.activeItemIndex;
            if (r !== null) {
                let p = e.items[r].dataRef.current.domRef, m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculateActiveIndex"])(i, {
                    resolveItems: ()=>e.items,
                    resolveActiveIndex: ()=>e.activeItemIndex,
                    resolveId: (u)=>u.id,
                    resolveDisabled: (u)=>u.dataRef.current.disabled
                });
                if (m !== null) {
                    let u = e.items[m].dataRef.current.domRef;
                    if (((o = p.current) == null ? void 0 : o.previousElementSibling) === u.current || ((d = u.current) == null ? void 0 : d.previousElementSibling) === null) return {
                        ...t,
                        activeItemIndex: m
                    };
                }
            }
        } else if (i.focus === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Next) {
            let r = e.activeItemIndex;
            if (r !== null) {
                let p = e.items[r].dataRef.current.domRef, m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculateActiveIndex"])(i, {
                    resolveItems: ()=>e.items,
                    resolveActiveIndex: ()=>e.activeItemIndex,
                    resolveId: (u)=>u.id,
                    resolveDisabled: (u)=>u.dataRef.current.disabled
                });
                if (m !== null) {
                    let u = e.items[m].dataRef.current.domRef;
                    if (((a = p.current) == null ? void 0 : a.nextElementSibling) === u.current || ((I = u.current) == null ? void 0 : I.nextElementSibling) === null) return {
                        ...t,
                        activeItemIndex: m
                    };
                }
            }
        }
        let n = S(e), s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculateActiveIndex"])(i, {
            resolveItems: ()=>n.items,
            resolveActiveIndex: ()=>n.activeItemIndex,
            resolveId: (r)=>r.id,
            resolveDisabled: (r)=>r.dataRef.current.disabled
        });
        return {
            ...t,
            ...n,
            activeItemIndex: s
        };
    },
    [3]: (e, i)=>{
        let n = e.searchQuery !== "" ? 0 : 1, s = e.searchQuery + i.value.toLowerCase(), o = (e.activeItemIndex !== null ? e.items.slice(e.activeItemIndex + n).concat(e.items.slice(0, e.activeItemIndex + n)) : e.items).find((a)=>{
            var I;
            return ((I = a.dataRef.current.textValue) == null ? void 0 : I.startsWith(s)) && !a.dataRef.current.disabled;
        }), d = o ? e.items.indexOf(o) : -1;
        return d === -1 || d === e.activeItemIndex ? {
            ...e,
            searchQuery: s
        } : {
            ...e,
            searchQuery: s,
            activeItemIndex: d,
            activationTrigger: 1
        };
    },
    [4] (e) {
        return e.searchQuery === "" ? e : {
            ...e,
            searchQuery: "",
            searchActiveItemIndex: null
        };
    },
    [5]: (e, i)=>{
        let t = e.items.concat(i.items.map((s)=>s)), n = e.activeItemIndex;
        return e.pendingFocus.focus !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Nothing && (n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculateActiveIndex"])(e.pendingFocus, {
            resolveItems: ()=>t,
            resolveActiveIndex: ()=>e.activeItemIndex,
            resolveId: (s)=>s.id,
            resolveDisabled: (s)=>s.dataRef.current.disabled
        })), {
            ...e,
            items: t,
            activeItemIndex: n,
            pendingFocus: {
                focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Nothing
            },
            pendingShouldSort: !0
        };
    },
    [6]: (e, i)=>{
        let t = e.items, n = [], s = new Set(i.items);
        for (let [l, o] of t.entries())if (s.has(o.id) && (n.push(l), s.delete(o.id), s.size === 0)) break;
        if (n.length > 0) {
            t = t.slice();
            for (let l of n.reverse())t.splice(l, 1);
        }
        return {
            ...e,
            items: t,
            activationTrigger: 1
        };
    },
    [7]: (e, i)=>e.buttonElement === i.element ? e : {
            ...e,
            buttonElement: i.element
        },
    [8]: (e, i)=>e.itemsElement === i.element ? e : {
            ...e,
            itemsElement: i.element
        },
    [9]: (e)=>e.pendingShouldSort ? {
            ...e,
            ...S(e),
            pendingShouldSort: !1
        } : e
};
class x extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Machine"] {
    constructor(t){
        super(t);
        g(this, "actions", {
            registerItem: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["batch"])(()=>{
                let t = [], n = new Set;
                return [
                    (s, l)=>{
                        n.has(l) || (n.add(l), t.push({
                            id: s,
                            dataRef: l
                        }));
                    },
                    ()=>(n.clear(), this.send({
                            type: 5,
                            items: t.splice(0)
                        }))
                ];
            }),
            unregisterItem: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["batch"])(()=>{
                let t = [];
                return [
                    (n)=>t.push(n),
                    ()=>this.send({
                            type: 6,
                            items: t.splice(0)
                        })
                ];
            })
        });
        g(this, "selectors", {
            activeDescendantId (t) {
                var l;
                let n = t.activeItemIndex, s = t.items;
                return n === null || (l = s[n]) == null ? void 0 : l.id;
            },
            isActive (t, n) {
                var o;
                let s = t.activeItemIndex, l = t.items;
                return s !== null ? ((o = l[s]) == null ? void 0 : o.id) === n : !1;
            },
            shouldScrollIntoView (t, n) {
                return t.__demoMode || t.menuState !== 0 || t.activationTrigger === 0 ? !1 : this.isActive(t, n);
            }
        });
        this.on(5, ()=>{
            this.disposables.requestAnimationFrame(()=>{
                this.send({
                    type: 9
                });
            });
        });
        {
            let n = this.state.id, s = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machines$2f$stack$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stackMachines"].get(null);
            this.disposables.add(s.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machines$2f$stack$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].Push, (l)=>{
                !s.selectors.isTop(l, n) && this.state.menuState === 0 && this.send({
                    type: 1
                });
            })), this.on(0, ()=>s.actions.push(n)), this.on(1, ()=>s.actions.pop(n));
        }
    }
    static new({ id: t, __demoMode: n = !1 }) {
        return new x({
            id: t,
            __demoMode: n,
            menuState: n ? 0 : 1,
            buttonElement: null,
            itemsElement: null,
            items: [],
            searchQuery: "",
            activeItemIndex: null,
            activationTrigger: 1,
            pendingShouldSort: !1,
            pendingFocus: {
                focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Nothing
            }
        });
    }
    reduce(t, n) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["match"])(n.type, D, t, n);
    }
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/components/menu/menu-machine-glue.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MenuContext": (()=>a),
    "useMenuMachine": (()=>s),
    "useMenuMachineContext": (()=>p)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$on$2d$unmount$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-on-unmount.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/components/menu/menu-machine.js [app-ssr] (ecmascript)");
;
;
;
const a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
function p(t) {
    let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(a);
    if (n === null) {
        let e = new Error(`<${t} /> is missing a parent <Menu /> component.`);
        throw Error.captureStackTrace && Error.captureStackTrace(e, s), e;
    }
    return n;
}
function s({ id: t, __demoMode: n = !1 }) {
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuMachine"].new({
            id: t,
            __demoMode: n
        }), []);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$on$2d$unmount$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOnUnmount"])(()=>e.dispose()), e;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/components/menu/menu.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Menu": (()=>lo),
    "MenuButton": (()=>yt),
    "MenuHeading": (()=>Mt),
    "MenuItem": (()=>Et),
    "MenuItems": (()=>Pt),
    "MenuSection": (()=>gt),
    "MenuSeparator": (()=>bt)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$aria$2f$focus$2f$dist$2f$useFocusRing$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-aria/focus/dist/useFocusRing.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$aria$2f$interactions$2f$dist$2f$useHover$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-aria/interactions/dist/useHover.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$active$2d$press$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-active-press.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$did$2d$element$2d$move$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-did-element-move.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$element$2d$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-element-size.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$inert$2d$others$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-inert-others.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$on$2d$disappear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-on-disappear.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$outside$2d$click$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-outside-click.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-owner.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$quick$2d$release$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-quick-release.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$resolve$2d$button$2d$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-resolve-button-type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$scroll$2d$lock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-scroll-lock.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-sync-refs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$text$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-text-value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$tracked$2d$pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-tracked-pointer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$transition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-transition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$tree$2d$walker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-tree-walker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$floating$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/internal/floating.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/internal/open-closed.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machines$2f$stack$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/machines/stack-machine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/react-glue.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$bugs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/bugs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/calculate-active-index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/focus-management.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/match.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/render.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$description$2f$description$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/components/description/description.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/components/keyboard.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$label$2f$label$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/components/label/label.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$portal$2f$portal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/components/portal/portal.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/components/menu/menu-machine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/components/menu/menu-machine-glue.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
let et = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"];
function tt(c, E) {
    let p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), { __demoMode: a = !1, ...s } = c, l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMenuMachine"])({
        id: p,
        __demoMode: a
    }), [n, g, y] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(l, (T)=>[
            T.menuState,
            T.itemsElement,
            T.buttonElement
        ]), I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(E), o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$machines$2f$stack$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stackMachines"].get(null), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(o, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((T)=>o.selectors.isTop(T, p), [
        o,
        p
    ]));
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$outside$2d$click$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOutsideClick"])(h, [
        y,
        g
    ], (T, u)=>{
        var f;
        l.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFocusableElement"])(u, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FocusableMode"].Loose) || (T.preventDefault(), (f = l.state.buttonElement) == null || f.focus());
    });
    let _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>{
        l.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
        });
    }), M = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            open: n === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open,
            close: _
        }), [
        n,
        _
    ]), i = {
        ref: I
    }, b = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$floating$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FloatingProvider"], null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuContext"].Provider, {
        value: l
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OpenClosedProvider"], {
        value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["match"])(n, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open]: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Open,
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Closed]: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Closed
        })
    }, b({
        ourProps: i,
        theirProps: s,
        slot: M,
        defaultTag: et,
        name: "Menu"
    }))));
}
let ot = "button";
function nt(c, E) {
    let p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMenuMachineContext"])("Menu.Button"), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), { id: s = `headlessui-menu-button-${a}`, disabled: l = !1, autoFocus: n = !1, ...g } = c, y = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$floating$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFloatingReferenceProps"])(), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(E, y, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$floating$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFloatingReference"])(), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>p.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].SetButtonElement,
            element: e
        }))), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>{
        switch(e.key){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Space:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Enter:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].ArrowDown:
                e.preventDefault(), e.stopPropagation(), p.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].OpenMenu,
                    focus: {
                        focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].First
                    }
                });
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].ArrowUp:
                e.preventDefault(), e.stopPropagation(), p.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].OpenMenu,
                    focus: {
                        focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Last
                    }
                });
                break;
        }
    }), _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>{
        switch(e.key){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Space:
                e.preventDefault();
                break;
        }
    }), [M, i, b] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(p, (e)=>[
            e.menuState,
            e.buttonElement,
            e.itemsElement
        ]), T = M === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$quick$2d$release$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuickRelease"])(T, {
        trigger: i,
        action: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
            if (i != null && i.contains(e.target)) return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$quick$2d$release$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Action"].Ignore;
            let R = e.target.closest('[role="menuitem"]:not([data-disabled])');
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isHTMLElement"])(R) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$quick$2d$release$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Action"].Select(R) : b != null && b.contains(e.target) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$quick$2d$release$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Action"].Ignore : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$quick$2d$release$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Action"].Close;
        }, [
            i,
            b
        ]),
        close: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>p.send({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
            }), []),
        select: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>e.click(), [])
    });
    let u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>{
        var R;
        if (e.button === 0) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$bugs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDisabledReactIssue7711"])(e.currentTarget)) return e.preventDefault();
            l || (M === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open ? ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["flushSync"])(()=>p.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
                })), (R = y.current) == null || R.focus({
                preventScroll: !0
            })) : (e.preventDefault(), p.send({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].OpenMenu,
                focus: {
                    focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Nothing
                },
                trigger: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActivationTrigger"].Pointer
            })));
        }
    }), { isFocusVisible: f, focusProps: v } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$aria$2f$focus$2f$dist$2f$useFocusRing$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFocusRing"])({
        autoFocus: n
    }), { isHovered: S, hoverProps: O } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$aria$2f$interactions$2f$dist$2f$useHover$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useHover"])({
        isDisabled: l
    }), { pressed: F, pressProps: U } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$active$2d$press$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useActivePress"])({
        disabled: l
    }), H = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            open: M === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open,
            active: F || M === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open,
            disabled: l,
            hover: S,
            focus: f,
            autofocus: n
        }), [
        M,
        S,
        f,
        F,
        l,
        n
    ]), G = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeProps"])(I(), {
        ref: o,
        id: s,
        type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$resolve$2d$button$2d$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useResolveButtonType"])(c, y.current),
        "aria-haspopup": "menu",
        "aria-controls": b == null ? void 0 : b.id,
        "aria-expanded": M === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open,
        disabled: l || void 0,
        autoFocus: n,
        onKeyDown: h,
        onKeyUp: _,
        onPointerDown: u
    }, v, O, U);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])()({
        ourProps: G,
        theirProps: g,
        slot: H,
        defaultTag: ot,
        name: "Menu.Button"
    });
}
let rt = "div", at = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderFeatures"].RenderStrategy | __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderFeatures"].Static;
function st(c, E) {
    let p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), { id: a = `headlessui-menu-items-${p}`, anchor: s, portal: l = !1, modal: n = !0, transition: g = !1, ...y } = c, I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$floating$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useResolvedAnchor"])(s), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMenuMachineContext"])("Menu.Items"), [h, _] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$floating$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFloatingPanel"])(I), M = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$floating$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useFloatingPanelProps"])(), [i, b] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), T = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(E, I ? h : null, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((t)=>o.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].SetItemsElement,
            element: t
        })), b), [u, f] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(o, (t)=>[
            t.menuState,
            t.buttonElement
        ]), v = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOwnerDocument"])(f), S = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$owner$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOwnerDocument"])(i);
    I && (l = !0);
    let O = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOpenClosed"])(), [F, U] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$transition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTransition"])(g, i, O !== null ? (O & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Open) === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Open : u === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$on$2d$disappear$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOnDisappear"])(F, f, ()=>{
        o.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
        });
    });
    let H = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(o, (t)=>t.__demoMode), G = H ? !1 : n && u === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$scroll$2d$lock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useScrollLock"])(G, S);
    let w = H ? !1 : n && u === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$inert$2d$others$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useInertOthers"])(w, {
        allowed: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>[
                f,
                i
            ], [
            f,
            i
        ])
    });
    let e = u !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open, le = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$did$2d$element$2d$move$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDidElementMove"])(e, f) ? !1 : F;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let t = i;
        t && u === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open && t !== (S == null ? void 0 : S.activeElement) && t.focus({
            preventScroll: !0
        });
    }, [
        u,
        i,
        S
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$tree$2d$walker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTreeWalker"])(u === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open, {
        container: i,
        accept (t) {
            return t.getAttribute("role") === "menuitem" ? NodeFilter.FILTER_REJECT : t.hasAttribute("role") ? NodeFilter.FILTER_SKIP : NodeFilter.FILTER_ACCEPT;
        },
        walk (t) {
            t.setAttribute("role", "none");
        }
    });
    let z = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDisposables"])(), pe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((t)=>{
        var N, Y, Z;
        switch(z.dispose(), t.key){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Space:
                if (o.state.searchQuery !== "") return t.preventDefault(), t.stopPropagation(), o.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].Search,
                    value: t.key
                });
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Enter:
                if (t.preventDefault(), t.stopPropagation(), o.state.activeItemIndex !== null) {
                    let { dataRef: ce } = o.state.items[o.state.activeItemIndex];
                    (Y = (N = ce.current) == null ? void 0 : N.domRef.current) == null || Y.click();
                }
                o.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
                }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["restoreFocusIfNecessary"])(o.state.buttonElement);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].ArrowDown:
                return t.preventDefault(), t.stopPropagation(), o.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
                    focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Next
                });
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].ArrowUp:
                return t.preventDefault(), t.stopPropagation(), o.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
                    focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Previous
                });
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Home:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].PageUp:
                return t.preventDefault(), t.stopPropagation(), o.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
                    focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].First
                });
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].End:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].PageDown:
                return t.preventDefault(), t.stopPropagation(), o.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
                    focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Last
                });
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Escape:
                t.preventDefault(), t.stopPropagation(), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["flushSync"])(()=>o.send({
                        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
                    })), (Z = o.state.buttonElement) == null || Z.focus({
                    preventScroll: !0
                });
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Tab:
                t.preventDefault(), t.stopPropagation(), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["flushSync"])(()=>o.send({
                        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
                    })), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["focusFrom"])(o.state.buttonElement, t.shiftKey ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Previous : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Next);
                break;
            default:
                t.key.length === 1 && (o.send({
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].Search,
                    value: t.key
                }), z.setTimeout(()=>o.send({
                        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].ClearSearch
                    }), 350));
                break;
        }
    }), ie = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((t)=>{
        switch(t.key){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Keys"].Space:
                t.preventDefault();
                break;
        }
    }), ue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            open: u === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open
        }), [
        u
    ]), de = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeProps"])(I ? M() : {}, {
        "aria-activedescendant": (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(o, o.selectors.activeDescendantId),
        "aria-labelledby": (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(o, (t)=>{
            var N;
            return (N = t.buttonElement) == null ? void 0 : N.id;
        }),
        id: a,
        onKeyDown: pe,
        onKeyUp: ie,
        role: "menu",
        tabIndex: u === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MenuState"].Open ? 0 : void 0,
        ref: T,
        style: {
            ...y.style,
            ..._,
            "--button-width": (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$element$2d$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useElementSize"])(f, !0).width
        },
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$transition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transitionDataAttributes"])(U)
    }), me = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$portal$2f$portal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Portal"], {
        enabled: l ? c.static || F : !1,
        ownerDocument: v
    }, me({
        ourProps: de,
        theirProps: y,
        slot: ue,
        defaultTag: rt,
        features: at,
        visible: le,
        name: "Menu.Items"
    }));
}
let lt = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"];
function pt(c, E) {
    let p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), { id: a = `headlessui-menu-item-${p}`, disabled: s = !1, ...l } = c, n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMenuMachineContext"])("Menu.Item"), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(n, (e)=>n.selectors.isActive(e, a)), y = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(E, y), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$react$2d$glue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSlice"])(n, (e)=>n.selectors.shouldScrollIntoView(e, a));
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (o) return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["disposables"])().requestAnimationFrame(()=>{
            var e, R;
            (R = (e = y.current) == null ? void 0 : e.scrollIntoView) == null || R.call(e, {
                block: "nearest"
            });
        });
    }, [
        o,
        y
    ]);
    let h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$text$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTextValue"])(y), _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({
        disabled: s,
        domRef: y,
        get textValue () {
            return h();
        }
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        _.current.disabled = s;
    }, [
        _,
        s
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>(n.actions.registerItem(a, _), ()=>n.actions.unregisterItem(a)), [
        _,
        a
    ]);
    let M = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>{
        n.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
        });
    }), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>{
        if (s) return e.preventDefault();
        n.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].CloseMenu
        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$focus$2d$management$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["restoreFocusIfNecessary"])(n.state.buttonElement);
    }), b = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>{
        if (s) return n.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
            focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Nothing
        });
        n.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
            focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Specific,
            id: a
        });
    }), T = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$tracked$2d$pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTrackedPointer"])(), u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>{
        T.update(e), !s && (g || n.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
            focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Specific,
            id: a,
            trigger: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActivationTrigger"].Pointer
        }));
    }), f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>{
        T.wasMoved(e) && (s || g || n.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
            focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Specific,
            id: a,
            trigger: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActivationTrigger"].Pointer
        }));
    }), v = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((e)=>{
        T.wasMoved(e) && (s || g && n.send({
            type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$menu$2f$menu$2d$machine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionTypes"].GoToItem,
            focus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$calculate$2d$active$2d$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Focus"].Nothing
        }));
    }), [S, O] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$label$2f$label$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLabels"])(), [F, U] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$description$2f$description$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDescriptions"])(), H = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            active: g,
            focus: g,
            disabled: s,
            close: M
        }), [
        g,
        s,
        M
    ]), G = {
        id: a,
        ref: I,
        role: "menuitem",
        tabIndex: s === !0 ? void 0 : -1,
        "aria-disabled": s === !0 ? !0 : void 0,
        "aria-labelledby": S,
        "aria-describedby": F,
        disabled: void 0,
        onClick: i,
        onFocus: b,
        onPointerEnter: u,
        onMouseEnter: u,
        onPointerMove: f,
        onMouseMove: f,
        onPointerLeave: v,
        onMouseLeave: v
    }, w = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(O, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(U, null, w({
        ourProps: G,
        theirProps: l,
        slot: H,
        defaultTag: lt,
        name: "Menu.Item"
    })));
}
let it = "div";
function ut(c, E) {
    let [p, a] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$label$2f$label$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLabels"])(), s = c, l = {
        ref: E,
        "aria-labelledby": p,
        role: "group"
    }, n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(a, null, n({
        ourProps: l,
        theirProps: s,
        slot: {},
        defaultTag: it,
        name: "Menu.Section"
    }));
}
let dt = "header";
function mt(c, E) {
    let p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(), { id: a = `headlessui-menu-heading-${p}`, ...s } = c, l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$components$2f$label$2f$label$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLabelContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>l.register(a), [
        a,
        l.register
    ]);
    let n = {
        id: a,
        ref: E,
        role: "presentation",
        ...l.props
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])()({
        ourProps: n,
        theirProps: s,
        slot: {},
        defaultTag: dt,
        name: "Menu.Heading"
    });
}
let ct = "div";
function Tt(c, E) {
    let p = c, a = {
        ref: E,
        role: "separator"
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])()({
        ourProps: a,
        theirProps: p,
        slot: {},
        defaultTag: ct,
        name: "Menu.Separator"
    });
}
let ft = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(tt), yt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(nt), Pt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(st), Et = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(pt), gt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(ut), Mt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(mt), bt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(Tt), lo = Object.assign(ft, {
    Button: yt,
    Items: Pt,
    Item: Et,
    Section: gt,
    Heading: Mt,
    Separator: bt
});
;
}}),
"[project]/node_modules/@headlessui/react/dist/hooks/use-is-mounted.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useIsMounted": (()=>f)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
;
;
function f() {
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(!1);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>(e.current = !0, ()=>{
            e.current = !1;
        }), []), e;
}
;
}}),
"[project]/node_modules/@headlessui/react/dist/components/transition/transition.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Transition": (()=>ze),
    "TransitionChild": (()=>Fe)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-disposables.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-event.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$is$2d$mounted$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-is-mounted.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-latest-value.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$server$2d$handoff$2d$complete$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-sync-refs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$transition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/hooks/use-transition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/internal/open-closed.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$class$2d$names$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/class-names.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/match.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@headlessui/react/dist/utils/render.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
function ue(e) {
    var t;
    return !!(e.enter || e.enterFrom || e.enterTo || e.leave || e.leaveFrom || e.leaveTo) || ((t = e.as) != null ? t : de) !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"] || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Children.count(e.children) === 1;
}
let w = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
w.displayName = "TransitionContext";
var _e = ((n)=>(n.Visible = "visible", n.Hidden = "hidden", n))(_e || {});
function De() {
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(w);
    if (e === null) throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");
    return e;
}
function He() {
    let e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(M);
    if (e === null) throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");
    return e;
}
let M = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
M.displayName = "NestingContext";
function U(e) {
    return "children" in e ? U(e.children) : e.current.filter(({ el: t })=>t.current !== null).filter(({ state: t })=>t === "visible").length > 0;
}
function Te(e, t) {
    let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$latest$2d$value$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLatestValue"])(e), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]), S = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$is$2d$mounted$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsMounted"])(), R = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$disposables$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDisposables"])(), d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((o, i = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderStrategy"].Hidden)=>{
        let a = l.current.findIndex(({ el: s })=>s === o);
        a !== -1 && ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["match"])(i, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderStrategy"].Unmount] () {
                l.current.splice(a, 1);
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderStrategy"].Hidden] () {
                l.current[a].state = "hidden";
            }
        }), R.microTask(()=>{
            var s;
            !U(l) && S.current && ((s = n.current) == null || s.call(n));
        }));
    }), y = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((o)=>{
        let i = l.current.find(({ el: a })=>a === o);
        return i ? i.state !== "visible" && (i.state = "visible") : l.current.push({
            el: o,
            state: "visible"
        }), ()=>d(o, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderStrategy"].Unmount);
    }), C = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(Promise.resolve()), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({
        enter: [],
        leave: []
    }), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((o, i, a)=>{
        C.current.splice(0), t && (t.chains.current[i] = t.chains.current[i].filter(([s])=>s !== o)), t == null || t.chains.current[i].push([
            o,
            new Promise((s)=>{
                C.current.push(s);
            })
        ]), t == null || t.chains.current[i].push([
            o,
            new Promise((s)=>{
                Promise.all(h.current[i].map(([r, f])=>f)).then(()=>s());
            })
        ]), i === "enter" ? p.current = p.current.then(()=>t == null ? void 0 : t.wait.current).then(()=>a(i)) : a(i);
    }), v = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((o, i, a)=>{
        Promise.all(h.current[i].splice(0).map(([s, r])=>r)).then(()=>{
            var s;
            (s = C.current.shift()) == null || s();
        }).then(()=>a(i));
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            children: l,
            register: y,
            unregister: d,
            onStart: g,
            onStop: v,
            wait: p,
            chains: h
        }), [
        y,
        d,
        l,
        g,
        v,
        h,
        p
    ]);
}
let de = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], fe = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderFeatures"].RenderStrategy;
function Ae(e, t) {
    var ee, te;
    let { transition: n = !0, beforeEnter: l, afterEnter: S, beforeLeave: R, afterLeave: d, enter: y, enterFrom: C, enterTo: p, entered: h, leave: g, leaveFrom: v, leaveTo: o, ...i } = e, [a, s] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), f = ue(e), j = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(...f ? [
        r,
        t,
        s
    ] : t === null ? [] : [
        t
    ]), H = (ee = i.unmount) == null || ee ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderStrategy"].Unmount : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderStrategy"].Hidden, { show: u, appear: z, initial: K } = De(), [m, G] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(u ? "visible" : "hidden"), Q = He(), { register: A, unregister: I } = Q;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>A(r), [
        A,
        r
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (H === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RenderStrategy"].Hidden && r.current) {
            if (u && m !== "visible") {
                G("visible");
                return;
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["match"])(m, {
                ["hidden"]: ()=>I(r),
                ["visible"]: ()=>A(r)
            });
        }
    }, [
        m,
        r,
        A,
        I,
        u,
        H
    ]);
    let B = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$server$2d$handoff$2d$complete$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useServerHandoffComplete"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        if (f && B && m === "visible" && r.current === null) throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?");
    }, [
        r,
        m,
        B,
        f
    ]);
    let ce = K && !z, Y = z && u && K, W = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(!1), L = Te(()=>{
        W.current || (G("hidden"), I(r));
    }, Q), Z = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((k)=>{
        W.current = !0;
        let F = k ? "enter" : "leave";
        L.onStart(r, F, (_)=>{
            _ === "enter" ? l == null || l() : _ === "leave" && (R == null || R());
        });
    }), $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])((k)=>{
        let F = k ? "enter" : "leave";
        W.current = !1, L.onStop(r, F, (_)=>{
            _ === "enter" ? S == null || S() : _ === "leave" && (d == null || d());
        }), F === "leave" && !U(L) && (G("hidden"), I(r));
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        f && n || (Z(u), $(u));
    }, [
        u,
        f,
        n
    ]);
    let pe = (()=>!(!n || !f || !B || ce))(), [, T] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$transition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTransition"])(pe, a, u, {
        start: Z,
        end: $
    }), Ce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["compact"])({
        ref: j,
        className: ((te = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$class$2d$names$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["classNames"])(i.className, Y && y, Y && C, T.enter && y, T.enter && T.closed && C, T.enter && !T.closed && p, T.leave && g, T.leave && !T.closed && v, T.leave && T.closed && o, !T.transition && u && h)) == null ? void 0 : te.trim()) || void 0,
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$transition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transitionDataAttributes"])(T)
    }), N = 0;
    m === "visible" && (N |= __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Open), m === "hidden" && (N |= __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Closed), u && m === "hidden" && (N |= __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Opening), !u && m === "visible" && (N |= __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Closing);
    let he = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(M.Provider, {
        value: L
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OpenClosedProvider"], {
        value: N
    }, he({
        ourProps: Ce,
        theirProps: i,
        defaultTag: de,
        features: fe,
        visible: m === "visible",
        name: "Transition.Child"
    })));
}
function Ie(e, t) {
    let { show: n, appear: l = !1, unmount: S = !0, ...R } = e, d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), y = ue(e), C = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$sync$2d$refs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncRefs"])(...y ? [
        d,
        t
    ] : t === null ? [] : [
        t
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$server$2d$handoff$2d$complete$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useServerHandoffComplete"])();
    let p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOpenClosed"])();
    if (n === void 0 && p !== null && (n = (p & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Open) === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["State"].Open), n === void 0) throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");
    let [h, g] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(n ? "visible" : "hidden"), v = Te(()=>{
        n || g("hidden");
    }), [o, i] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!0), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([
        n
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        o !== !1 && a.current[a.current.length - 1] !== n && (a.current.push(n), i(!1));
    }, [
        a,
        n
    ]);
    let s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            show: n,
            appear: l,
            initial: o
        }), [
        n,
        l,
        o
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$iso$2d$morphic$2d$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsoMorphicEffect"])(()=>{
        n ? g("visible") : !U(v) && d.current !== null && g("hidden");
    }, [
        n,
        v
    ]);
    let r = {
        unmount: S
    }, f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>{
        var u;
        o && i(!1), (u = e.beforeEnter) == null || u.call(e);
    }), j = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$hooks$2f$use$2d$event$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEvent"])(()=>{
        var u;
        o && i(!1), (u = e.beforeLeave) == null || u.call(e);
    }), H = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRender"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(M.Provider, {
        value: v
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(w.Provider, {
        value: s
    }, H({
        ourProps: {
            ...r,
            as: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"],
            children: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(me, {
                ref: C,
                ...r,
                ...R,
                beforeEnter: f,
                beforeLeave: j
            })
        },
        theirProps: {},
        defaultTag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"],
        features: fe,
        visible: h === "visible",
        name: "Transition"
    })));
}
function Le(e, t) {
    let n = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(w) !== null, l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$internal$2f$open$2d$closed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOpenClosed"])() !== null;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, !n && l ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(X, {
        ref: t,
        ...e
    }) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(me, {
        ref: t,
        ...e
    }));
}
let X = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(Ie), me = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(Ae), Fe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$headlessui$2f$react$2f$dist$2f$utils$2f$render$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRefWithAs"])(Le), ze = Object.assign(X, {
    Child: Fe,
    Root: X
});
;
}}),

};

//# sourceMappingURL=node_modules_%40headlessui_react_dist_754fd85f._.js.map