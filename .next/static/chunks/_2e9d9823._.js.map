{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Library/CloudStorage/OneDrive-Personal/mywebmaster/startups/whatz-it/app/wihatz-Cursor-Vercel-v1/src/app/auth/signin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { supabase } from '@/lib/supabase'\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'\n\nexport default function SignIn() {\n  const router = useRouter()\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleEmailSignIn = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError('')\n\n    try {\n      const { error: signInError } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (signInError) {\n        setError(signInError.message)\n      } else {\n        router.push('/')\n      }\n    } catch {\n      setError('An unexpected error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleGoogleSignIn = async () => {\n    setIsLoading(true)\n    setError('')\n\n    try {\n      const { error: oauthError } = await supabase.auth.signInWithOAuth({\n        provider: 'google',\n        options: {\n          redirectTo: `${window.location.origin}/`\n        }\n      })\n\n      if (oauthError) {\n        setError(oauthError.message)\n      }\n    } catch {\n      setError('An unexpected error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-sm\">\n        <Link href=\"/\" className=\"flex justify-center\">\n          <span className=\"text-2xl font-bold text-blue-600\">WhatIsItLike.to</span>\n        </Link>\n        <h2 className=\"mt-10 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900\">\n          Sign in to your account\n        </h2>\n      </div>\n\n      <div className=\"mt-10 sm:mx-auto sm:w-full sm:max-w-sm\">\n        {error && (\n          <div className=\"mb-4 rounded-md bg-red-50 p-4\">\n            <div className=\"text-sm text-red-700\">{error}</div>\n          </div>\n        )}\n\n        <form className=\"space-y-6\" onSubmit={handleEmailSignIn}>\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium leading-6 text-gray-900\">\n              Email address\n            </label>\n            <div className=\"mt-2\">\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <div className=\"flex items-center justify-between\">\n              <label htmlFor=\"password\" className=\"block text-sm font-medium leading-6 text-gray-900\">\n                Password\n              </label>\n              <div className=\"text-sm\">\n                <Link href=\"/auth/forgot-password\" className=\"font-semibold text-blue-600 hover:text-blue-500\">\n                  Forgot password?\n                </Link>\n              </div>\n            </div>\n            <div className=\"mt-2 relative\">\n              <input\n                id=\"password\"\n                name=\"password\"\n                type={showPassword ? 'text' : 'password'}\n                autoComplete=\"current-password\"\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className=\"block w-full rounded-md border-0 py-1.5 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6\"\n              />\n              <button\n                type=\"button\"\n                className=\"absolute inset-y-0 right-0 flex items-center pr-3\"\n                onClick={() => setShowPassword(!showPassword)}\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                )}\n              </button>\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"flex w-full justify-center rounded-md bg-blue-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'Signing in...' : 'Sign in'}\n            </button>\n          </div>\n        </form>\n\n        <div className=\"mt-6\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"bg-white px-2 text-gray-500\">Or continue with</span>\n            </div>\n          </div>\n\n          <div className=\"mt-6\">\n            <button\n              onClick={handleGoogleSignIn}\n              disabled={isLoading}\n              className=\"flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <svg className=\"h-5 w-5\" viewBox=\"0 0 24 24\">\n                <path\n                  d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  fill=\"#4285F4\"\n                />\n                <path\n                  d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  fill=\"#34A853\"\n                />\n                <path\n                  d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  fill=\"#FBBC05\"\n                />\n                <path\n                  d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  fill=\"#EA4335\"\n                />\n              </svg>\n              Sign in with Google\n            </button>\n          </div>\n        </div>\n\n        <p className=\"mt-10 text-center text-sm text-gray-500\">\n          Don&apos;t have an account?{' '}\n          <Link href=\"/auth/signup\" className=\"font-semibold leading-6 text-blue-600 hover:text-blue-500\">\n            Sign up here\n          </Link>\n        </p>\n      </div>\n    </div>\n  )\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACpE;gBACA;YACF;YAEA,IAAI,aAAa;gBACf,SAAS,YAAY,OAAO;YAC9B,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBAChE,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC1C;YACF;YAEA,IAAI,YAAY;gBACd,SAAS,WAAW,OAAO;YAC7B;QACF,EAAE,OAAM;YACN,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;kCAErD,6LAAC;wBAAG,WAAU;kCAA8E;;;;;;;;;;;;0BAK9F,6LAAC;gBAAI,WAAU;;oBACZ,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;kCAI3C,6LAAC;wBAAK,WAAU;wBAAY,UAAU;;0CACpC,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAAoD;;;;;;kDAGrF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAAoD;;;;;;0DAGxF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAwB,WAAU;8DAAkD;;;;;;;;;;;;;;;;;kDAKnG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAM,eAAe,SAAS;gDAC9B,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,WAAU;;;;;;0DAEZ,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB,CAAC;0DAE/B,6BACC,6LAAC,0NAAA,CAAA,eAAY;oDAAC,WAAU;;;;;yEAExB,6LAAC,gNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM3B,6LAAC;0CACC,cAAA,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;kCAKrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;;0CAIlD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAU,SAAQ;;8DAC/B,6LAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6LAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6LAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6LAAC;oDACC,GAAE;oDACF,MAAK;;;;;;;;;;;;wCAEH;;;;;;;;;;;;;;;;;;kCAMZ,6LAAC;wBAAE,WAAU;;4BAA0C;4BACzB;0CAC5B,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAe,WAAU;0CAA4D;;;;;;;;;;;;;;;;;;;;;;;;AAO1G;GAzLwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Library/CloudStorage/OneDrive-Personal/mywebmaster/startups/whatz-it/app/wihatz-Cursor-Vercel-v1/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Library/CloudStorage/OneDrive-Personal/mywebmaster/startups/whatz-it/app/wihatz-Cursor-Vercel-v1/node_modules/%40heroicons/react/24/outline/esm/EyeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Library/CloudStorage/OneDrive-Personal/mywebmaster/startups/whatz-it/app/wihatz-Cursor-Vercel-v1/node_modules/%40heroicons/react/24/outline/esm/EyeSlashIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeSlashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeSlashIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}