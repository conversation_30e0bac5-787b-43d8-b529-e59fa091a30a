export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          username: string | null
          full_name: string | null
          bio: string | null
          avatar_url: string | null
          credibility_tags: string[] | null
          is_verified: boolean
        }
        Insert: {
          id: string
          created_at?: string
          updated_at?: string
          username?: string | null
          full_name?: string | null
          bio?: string | null
          avatar_url?: string | null
          credibility_tags?: string[] | null
          is_verified?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          username?: string | null
          full_name?: string | null
          bio?: string | null
          avatar_url?: string | null
          credibility_tags?: string[] | null
          is_verified?: boolean
        }
      }
      categories: {
        Row: {
          id: string
          created_at: string
          name: string
          description: string | null
          slug: string
          color: string
          icon: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          name: string
          description?: string | null
          slug: string
          color: string
          icon?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          name?: string
          description?: string | null
          slug?: string
          color?: string
          icon?: string | null
        }
      }
      questions: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          title: string
          description: string | null
          author_id: string
          category_id: string
          is_anonymous: boolean
          upvotes: number
          downvotes: number
          answer_count: number
          is_featured: boolean
          tags: string[] | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          title: string
          description?: string | null
          author_id: string
          category_id: string
          is_anonymous?: boolean
          upvotes?: number
          downvotes?: number
          answer_count?: number
          is_featured?: boolean
          tags?: string[] | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          title?: string
          description?: string | null
          author_id?: string
          category_id?: string
          is_anonymous?: boolean
          upvotes?: number
          downvotes?: number
          answer_count?: number
          is_featured?: boolean
          tags?: string[] | null
        }
      }
      answers: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          content: string
          question_id: string
          author_id: string
          is_anonymous: boolean
          upvotes: number
          downvotes: number
          is_best_answer: boolean
          credibility_info: string | null
          video_url: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          content: string
          question_id: string
          author_id: string
          is_anonymous?: boolean
          upvotes?: number
          downvotes?: number
          is_best_answer?: boolean
          credibility_info?: string | null
          video_url?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          content?: string
          question_id?: string
          author_id?: string
          is_anonymous?: boolean
          upvotes?: number
          downvotes?: number
          is_best_answer?: boolean
          credibility_info?: string | null
          video_url?: string | null
        }
      }
      votes: {
        Row: {
          id: string
          created_at: string
          user_id: string
          question_id: string | null
          answer_id: string | null
          vote_type: 'up' | 'down'
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          question_id?: string | null
          answer_id?: string | null
          vote_type: 'up' | 'down'
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          question_id?: string | null
          answer_id?: string | null
          vote_type?: 'up' | 'down'
        }
      }
      ama_sessions: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          title: string
          description: string
          host_id: string
          category_id: string
          scheduled_at: string
          duration_minutes: number
          status: 'upcoming' | 'live' | 'ended'
          max_questions: number | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          title: string
          description: string
          host_id: string
          category_id: string
          scheduled_at: string
          duration_minutes: number
          status?: 'upcoming' | 'live' | 'ended'
          max_questions?: number | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          title?: string
          description?: string
          host_id?: string
          category_id?: string
          scheduled_at?: string
          duration_minutes?: number
          status?: 'upcoming' | 'live' | 'ended'
          max_questions?: number | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
} 