import Link from 'next/link'
import { 
  ChevronUpIcon,
  ChevronDownIcon,
  ChatBubbleLeftRightIcon,
  ArrowTrendingUpIcon,
  UserGroupIcon,
  SparklesIcon,
  FireIcon,
  ClockIcon,
  StarIcon,
  ShareIcon
} from '@heroicons/react/24/outline'

// Mock data - in real app this would come from database
const feedQuestions = [
  {
    id: '1',
    title: 'What is it like to work remotely from different countries?',
    category: 'Career',
    categoryColor: 'text-blue-600',
    author: 'digitalnomad_jane',
    comments: 24,
    upvotes: 156,
    downvotes: 12,
    timeAgo: '2h',
    excerpt: 'The freedom is incredible but tax implications can be complex. I&apos;ve been working remotely while traveling for 3 years now, and here&apos;s what I&apos;ve learned...',
    tags: ['remote-work', 'travel', 'taxes', 'lifestyle']
  },
  {
    id: '2',
    title: 'What is it like to have ADHD as an adult?',
    category: 'Health',
    categoryColor: 'text-green-600',
    author: 'mindfulbrain',
    comments: 89,
    upvotes: 432,
    downvotes: 8,
    timeAgo: '4h',
    excerpt: 'It&apos;s like having a browser with 47 tabs open, and 3 of them are playing music. Getting diagnosed at 28 changed everything for me...',
    tags: ['adhd', 'mental-health', 'adult-diagnosis', 'medication']
  },
  {
    id: '3',
    title: 'What is it like to live in Iceland?',
    category: 'Countries',
    categoryColor: 'text-purple-600',
    author: 'reykjavik_resident',
    comments: 67,
    upvotes: 289,
    downvotes: 15,
    timeAgo: '6h',
    excerpt: 'The natural beauty is unmatched, but groceries will shock you. Housing is expensive but the social safety net is incredible...',
    tags: ['iceland', 'cost-of-living', 'culture', 'weather']
  },
  {
    id: '4',
    title: 'What is it like to quit your corporate job to start freelancing?',
    category: 'Career',
    categoryColor: 'text-blue-600',
    author: 'former_corporate',
    comments: 43,
    upvotes: 178,
    downvotes: 22,
    timeAgo: '8h',
    excerpt: 'Terrifying and liberating at the same time. The financial uncertainty is real, but so is the freedom to choose your projects...',
    tags: ['freelancing', 'career-change', 'entrepreneurship', 'financial-planning']
  },
  {
    id: '5',
    title: 'What is it like to be a single parent?',
    category: 'Relationships',
    categoryColor: 'text-pink-600',
    author: 'solo_parent_journey',
    comments: 156,
    upvotes: 523,
    downvotes: 7,
    timeAgo: '12h',
    excerpt: 'Exhausting but rewarding. You become incredibly resourceful and your relationship with your kids is unique...',
    tags: ['parenting', 'single-parent', 'work-life-balance', 'support']
  }
]

const popularCommunities = [
  { name: 'Career', members: '1.2M', color: 'bg-blue-500', trending: true },
  { name: 'Countries', members: '892K', color: 'bg-purple-500', trending: false },
  { name: 'Health', members: '756K', color: 'bg-green-500', trending: true },
  { name: 'Lifestyle', members: '643K', color: 'bg-orange-500', trending: false },
  { name: 'Education', members: '521K', color: 'bg-indigo-500', trending: false },
  { name: 'Relationships', members: '489K', color: 'bg-pink-500', trending: true },
  { name: 'Technology', members: '387K', color: 'bg-gray-500', trending: false },
  { name: 'Finance', members: '298K', color: 'bg-yellow-500', trending: false }
]

const trendingTopics = [
  { tag: 'remote-work', questions: 234 },
  { tag: 'mental-health', questions: 189 },
  { tag: 'career-change', questions: 156 },
  { tag: 'iceland', questions: 89 },
  { tag: 'freelancing', questions: 67 }
]

const stats = [
  { name: 'Questions Today', value: '1,247', change: '+12%' },
  { name: 'Active Members', value: '48.3K', change: '+5%' },
  { name: 'Expert Contributors', value: '2,847', change: '+8%' },
]

export default function Home() {
  return (
    <div className="flex gap-4 lg:gap-6 max-w-7xl mx-auto">
      {/* Left Sidebar */}
      <div className="hidden lg:block w-80 flex-shrink-0">
        <div className="sticky top-6 space-y-4">
          {/* Popular Communities */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <h3 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                <FireIcon className="h-4 w-4 text-orange-500" />
                Popular Communities
              </h3>
            </div>
            <div className="p-2">
              {popularCommunities.slice(0, 6).map((community, index) => (
                <Link
                  key={community.name}
                  href={`/communities/${community.name.toLowerCase()}`}
                  className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-50 group"
                >
                  <span className="text-sm font-medium text-gray-600">{index + 1}</span>
                  <div className={`w-3 h-3 rounded-full ${community.color}`}></div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                        {community.name}
                      </span>
                      {community.trending && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                          Hot
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-gray-500">{community.members} members</span>
                  </div>
                </Link>
              ))}
            </div>
            <div className="px-4 py-2 border-t border-gray-200">
              <Link
                href="/communities"
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                View all communities
              </Link>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="space-y-3">
              <Link
                href="/ask"
                className="flex items-center gap-3 p-3 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors"
              >
                <SparklesIcon className="h-5 w-5" />
                <span className="font-medium">Ask a Question</span>
              </Link>
              <Link
                href="/trending"
                className="flex items-center gap-3 p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <ArrowTrendingUpIcon className="h-5 w-5 text-orange-500" />
                <span className="font-medium text-gray-900">Trending Now</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Feed */}
      <div className="flex-1 max-w-2xl">
        {/* Feed Header */}
        <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-gray-900">Popular Questions</h1>
            <div className="flex items-center gap-2">
              <button className="px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                Hot
              </button>
              <button className="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-md">
                New
              </button>
              <button className="px-3 py-1.5 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-md">
                Top
              </button>
            </div>
          </div>
        </div>

        {/* Questions Feed */}
        <div className="space-y-4">
          {feedQuestions.map((question) => (
            <div
              key={question.id}
              className="bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
            >
              <div className="flex">
                {/* Voting Column */}
                <div className="flex flex-col items-center p-2 bg-gray-50 rounded-l-lg">
                  <button 
                    className="p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-orange-500"
                    aria-label="Upvote question"
                  >
                    <ChevronUpIcon className="h-5 w-5" />
                  </button>
                  <span className="text-sm font-semibold text-gray-900 py-1">
                    {question.upvotes - question.downvotes}
                  </span>
                  <button 
                    className="p-1 rounded hover:bg-gray-200 text-gray-400 hover:text-blue-500"
                    aria-label="Downvote question"
                  >
                    <ChevronDownIcon className="h-5 w-5" />
                  </button>
                </div>

                {/* Content */}
                <div className="flex-1 p-4">
                  {/* Header */}
                  <div className="flex items-center gap-2 text-xs text-gray-500 mb-2">
                    <Link href={`/communities/${question.category.toLowerCase()}`} className={`font-medium ${question.categoryColor} hover:underline`}>
                      {question.category}
                    </Link>
                    <span>•</span>
                    <span>Posted by {question.author}</span>
                    <span>•</span>
                    <span className="flex items-center gap-1">
                      <ClockIcon className="h-3 w-3" />
                      {question.timeAgo}
                    </span>
                  </div>

                  {/* Title */}
                  <h2 className="text-lg font-semibold text-gray-900 mb-2 hover:text-blue-600">
                    <Link href={`/questions/${question.id}`}>
                      {question.title}
                    </Link>
                  </h2>

                  {/* Excerpt */}
                  <p className="text-gray-700 mb-3 line-clamp-2">
                    {question.excerpt}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {question.tags.map((tag) => (
                      <Link
                        key={tag}
                        href={`/tags/${tag}`}
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200"
                      >
                        #{tag}
                      </Link>
                    ))}
                  </div>

                  {/* Footer */}
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <Link
                      href={`/questions/${question.id}#answers`}
                      className="flex items-center gap-1 hover:text-gray-700"
                    >
                      <ChatBubbleLeftRightIcon className="h-4 w-4" />
                      {question.comments} comments
                    </Link>
                    <button className="flex items-center gap-1 hover:text-gray-700">
                      <StarIcon className="h-4 w-4" />
                      Save
                    </button>
                    <button className="flex items-center gap-1 hover:text-gray-700">
                      <ShareIcon className="h-4 w-4" />
                      Share
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More */}
        <div className="mt-6 text-center">
          <button className="px-6 py-2 bg-white border border-gray-200 rounded-md text-gray-700 hover:bg-gray-50 font-medium">
            Load more questions
          </button>
        </div>
      </div>

      {/* Right Sidebar */}
      <div className="hidden xl:block w-80 flex-shrink-0">
        <div className="sticky top-6 space-y-4">
          {/* Welcome Banner */}
          <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg p-6 text-white">
            <h3 className="text-lg font-bold mb-2">Welcome to Whatsitlike.to</h3>
            <p className="text-sm text-blue-100 mb-4">
              Share real experiences and get authentic insights from people who&apos;ve been there.
            </p>
            <Link
              href="/ask"
              className="inline-flex items-center px-4 py-2 bg-white text-blue-600 rounded-md text-sm font-medium hover:bg-gray-50"
            >
              Ask Your First Question
            </Link>
          </div>

          {/* Today's Stats */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <h3 className="text-sm font-semibold text-gray-900">Today&apos;s Activity</h3>
            </div>
            <div className="p-4 space-y-3">
              {stats.map((stat) => (
                <div key={stat.name} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">{stat.name}</p>
                    <p className="text-lg font-semibold text-gray-900">{stat.value}</p>
                  </div>
                  <span className="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded">
                    {stat.change}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Trending Topics */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <h3 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                <ArrowTrendingUpIcon className="h-4 w-4 text-orange-500" />
                Trending Topics
              </h3>
            </div>
            <div className="p-2">
              {trendingTopics.map((topic, index) => (
                <Link
                  key={topic.tag}
                  href={`/tags/${topic.tag}`}
                  className="flex items-center justify-between p-2 rounded-md hover:bg-gray-50 group"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-gray-600">{index + 1}</span>
                    <span className="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                      #{topic.tag}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">{topic.questions} questions</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Rules/Guidelines */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Community Guidelines</h3>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Be respectful and kind to others</li>
              <li>• Share authentic experiences only</li>
              <li>• No spam or self-promotion</li>
              <li>• Protect privacy and anonymity</li>
              <li>• Use appropriate content warnings</li>
            </ul>
            <Link
              href="/guidelines"
              className="text-xs text-blue-600 hover:text-blue-700 font-medium mt-2 inline-block"
            >
              Read full guidelines →
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
