import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/Navigation";
import { Footer } from "@/components/Footer";
import { AuthProvider } from "@/components/AuthProvider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Whatsitlike.to - Experience Everything",
  description: "A social Q&A platform where people share real experiences. Ask anything: What is it like to live in Iceland? Have ADHD? Leave corporate for freelancing?",
  keywords: "experiences, questions, answers, social, community, advice, life experiences",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50`}>
        <AuthProvider>
          <div className="min-h-full flex flex-col">
            <Navigation />
            <main className="flex-1 py-10">
              <div className="mx-auto px-4 sm:px-6 lg:px-8">
                {children}
              </div>
            </main>
            <Footer />
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
