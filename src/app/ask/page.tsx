'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/AuthProvider'
import { 
  InformationCircleIcon,
  EyeSlashIcon,
  ChatBubbleLeftEllipsisIcon
} from '@heroicons/react/24/outline'

const categories = [
  { id: '1', name: 'Career', description: 'Work, jobs, professional life' },
  { id: '2', name: 'Countries', description: 'Living abroad, travel, cultures' },
  { id: '3', name: 'Health', description: 'Medical conditions, wellness, mental health' },
  { id: '4', name: 'Lifestyle', description: 'Daily life, habits, personal choices' },
  { id: '5', name: 'Education', description: 'Schools, learning, academic life' },
  { id: '6', name: 'Relationships', description: 'Family, friends, romantic relationships' },
  { id: '7', name: 'Technology', description: 'Tech industry, programming, digital life' },
  { id: '8', name: 'Finance', description: 'Money, investments, financial decisions' }
]

const questionExamples = [
  "What is it like to work a 4-day work week?",
  "What is it like to live with chronic pain?",
  "What is it like to move to Japan as a foreigner?",
  "What is it like to start your own business?",
  "What is it like to be homeschooled?",
  "What is it like to go through a divorce?",
  "What is it like to work at a startup?",
  "What is it like to live off-grid?"
]

export default function AskQuestion() {
  const { user } = useAuth()
  const router = useRouter()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    tags: '',
    isAnonymous: false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) {
      router.push('/auth/signin')
      return
    }

    setIsSubmitting(true)
    try {
      // TODO: Submit to database
      console.log('Submitting question:', formData)
      // Redirect to the question page after creation
      router.push('/')
    } catch (error) {
      console.error('Error submitting question:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="mx-auto max-w-4xl">
      <div className="md:flex md:items-center md:justify-between mb-8">
        <div className="min-w-0 flex-1">
          <h1 className="text-3xl font-bold leading-7 text-gray-900 sm:truncate sm:text-4xl sm:tracking-tight">
            Ask Your Question
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Share your curiosity with our community and get insights from people with real experiences.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
              <div className="px-4 py-6 sm:p-8">
                <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                  {/* Question Title */}
                  <div className="sm:col-span-6">
                    <label htmlFor="title" className="block text-sm font-medium leading-6 text-gray-900">
                      Question Title
                    </label>
                    <div className="mt-2">
                      <input
                        type="text"
                        name="title"
                        id="title"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                        placeholder="What is it like to..."
                        required
                      />
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Start with &ldquo;What is it like to...&rdquo; for the best responses.
                    </p>
                  </div>

                  {/* Category Selection */}
                  <div className="sm:col-span-6">
                    <label htmlFor="category" className="block text-sm font-medium leading-6 text-gray-900">
                      Category
                    </label>
                    <div className="mt-2">
                      <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={(e) => handleInputChange('category', e.target.value)}
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                        required
                      >
                        <option value="">Select a category</option>
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.name} - {category.description}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Description */}
                  <div className="sm:col-span-6">
                    <label htmlFor="description" className="block text-sm font-medium leading-6 text-gray-900">
                      Additional Details
                    </label>
                    <div className="mt-2">
                      <textarea
                        id="description"
                        name="description"
                        rows={4}
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                        placeholder="Provide more context about what specifically you'd like to know..."
                      />
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Optional: Add more details to help people give you better answers.
                    </p>
                  </div>

                  {/* Tags */}
                  <div className="sm:col-span-6">
                    <label htmlFor="tags" className="block text-sm font-medium leading-6 text-gray-900">
                      Tags
                    </label>
                    <div className="mt-2">
                      <input
                        type="text"
                        name="tags"
                        id="tags"
                        value={formData.tags}
                        onChange={(e) => handleInputChange('tags', e.target.value)}
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                        placeholder="remote-work, freelancing, career-change"
                      />
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Separate tags with commas to help people find your question.
                    </p>
                  </div>

                  {/* Anonymous Option */}
                  <div className="sm:col-span-6">
                    <div className="relative flex items-start">
                      <div className="flex h-6 items-center">
                        <input
                          id="anonymous"
                          name="anonymous"
                          type="checkbox"
                          checked={formData.isAnonymous}
                          onChange={(e) => handleInputChange('isAnonymous', e.target.checked)}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"
                        />
                      </div>
                      <div className="ml-3 text-sm leading-6">
                        <label htmlFor="anonymous" className="font-medium text-gray-900">
                          Post anonymously
                        </label>
                        <p className="text-gray-500">
                          Your username won&apos;t be shown with this question.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-end gap-x-6 border-t border-gray-900/10 px-4 py-4 sm:px-8">
                <button
                  type="button"
                  className="text-sm font-semibold leading-6 text-gray-900"
                  onClick={() => router.back()}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || !formData.title || !formData.category}
                  className="rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Posting...' : 'Post Question'}
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Tips */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start">
              <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">Tips for great questions</h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Be specific about what you want to know</li>
                    <li>Include relevant context or background</li>
                    <li>Ask about personal experiences, not facts</li>
                    <li>Consider adding tags to reach the right audience</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Question Examples */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center mb-3">
              <ChatBubbleLeftEllipsisIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900">Popular questions</h3>
            </div>
            <div className="space-y-2">
              {questionExamples.slice(0, 5).map((example, index) => (
                <button
                  key={index}
                  onClick={() => handleInputChange('title', example)}
                  className="block w-full text-left text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded p-2 transition-colors"
                >
                  {example}
                </button>
              ))}
            </div>
          </div>

          {/* Privacy Info */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <div className="flex items-start">
              <EyeSlashIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Your privacy</h3>
                <p className="mt-2 text-sm text-gray-600">
                  Questions are public by default. Check &ldquo;Post anonymously&rdquo; if you prefer not to show your username.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 