'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/AuthProvider'
import {
  InformationCircleIcon,
  EyeSlashIcon,
  ChatBubbleLeftEllipsisIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

const categories = [
  { id: '1', name: 'Career', description: 'Work, jobs, professional life' },
  { id: '2', name: 'Countries', description: 'Living abroad, travel, cultures' },
  { id: '3', name: 'Health', description: 'Medical conditions, wellness, mental health' },
  { id: '4', name: 'Lifestyle', description: 'Daily life, habits, personal choices' },
  { id: '5', name: 'Education', description: 'Schools, learning, academic life' },
  { id: '6', name: 'Relationships', description: 'Family, friends, romantic relationships' },
  { id: '7', name: 'Technology', description: 'Tech industry, programming, digital life' },
  { id: '8', name: 'Finance', description: 'Money, investments, financial decisions' }
]

const questionExamples = [
  "What is it like to work a 4-day work week?",
  "What is it like to live with chronic pain?",
  "What is it like to move to Japan as a foreigner?",
  "What is it like to start your own business?",
  "What is it like to be homeschooled?",
  "What is it like to go through a divorce?",
  "What is it like to work at a startup?",
  "What is it like to live off-grid?"
]

export default function AskQuestion() {
  const { user } = useAuth()
  const router = useRouter()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    tags: '',
    isAnonymous: false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [similarQuestions, setSimilarQuestions] = useState<any[]>([])
  const [showSimilar, setShowSimilar] = useState(false)
  const [titleError, setTitleError] = useState('')
  const [isValidTitle, setIsValidTitle] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) {
      router.push('/auth/signin')
      return
    }

    setIsSubmitting(true)
    try {
      // TODO: Submit to database
      console.log('Submitting question:', formData)
      // Clear draft after successful submission
      clearDraft()
      // Redirect to the question page after creation
      router.push('/')
    } catch (error) {
      console.error('Error submitting question:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Auto-save draft to localStorage
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.title || formData.description) {
        localStorage.setItem('question-draft', JSON.stringify(formData))
      }
    }, 2000)
    return () => clearTimeout(timer)
  }, [formData])

  // Load draft on component mount
  useEffect(() => {
    const draft = localStorage.getItem('question-draft')
    if (draft) {
      try {
        const parsedDraft = JSON.parse(draft)
        setFormData(parsedDraft)
      } catch (error) {
        console.error('Error loading draft:', error)
      }
    }
  }, [])

  // Title validation
  useEffect(() => {
    if (formData.title.length === 0) {
      setTitleError('')
      setIsValidTitle(false)
      return
    }

    if (formData.title.length < 10) {
      setTitleError('Title should be at least 10 characters long')
      setIsValidTitle(false)
    } else if (formData.title.length > 200) {
      setTitleError('Title should be less than 200 characters')
      setIsValidTitle(false)
    } else if (!formData.title.toLowerCase().includes('what is it like')) {
      setTitleError('Consider starting with "What is it like to..." for better responses')
      setIsValidTitle(true) // Still valid, just a suggestion
    } else {
      setTitleError('')
      setIsValidTitle(true)
    }

    // Simulate similar question detection
    if (formData.title.length > 20) {
      const mockSimilar = [
        { id: '1', title: 'What is it like to work remotely from different countries?', votes: 156 },
        { id: '2', title: 'What is it like to be a digital nomad?', votes: 89 }
      ]
      setSimilarQuestions(mockSimilar)
      setShowSimilar(true)
    } else {
      setShowSimilar(false)
    }
  }, [formData.title])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const clearDraft = () => {
    localStorage.removeItem('question-draft')
  }

  return (
    <div className="mx-auto max-w-4xl">
      <div className="md:flex md:items-center md:justify-between mb-8">
        <div className="min-w-0 flex-1">
          <h1 className="text-3xl font-bold leading-7 text-gray-900 sm:truncate sm:text-4xl sm:tracking-tight">
            Ask Your Question
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Share your curiosity with our community and get insights from people with real experiences.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
              <div className="px-4 py-6 sm:p-8">
                <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                  {/* Question Title */}
                  <div className="sm:col-span-6">
                    <label htmlFor="title" className="block text-sm font-medium leading-6 text-gray-900">
                      Question Title
                    </label>
                    <div className="mt-2">
                      <div className="relative">
                        <input
                          type="text"
                          name="title"
                          id="title"
                          value={formData.title}
                          onChange={(e) => handleInputChange('title', e.target.value)}
                          className={`block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ${
                            titleError && !isValidTitle
                              ? 'ring-red-300 focus:ring-red-600'
                              : isValidTitle
                                ? 'ring-green-300 focus:ring-green-600'
                                : 'ring-gray-300 focus:ring-blue-600'
                          } placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6`}
                          placeholder="What is it like to..."
                          required
                        />
                        {isValidTitle && (
                          <CheckCircleIcon className="absolute right-3 top-2 h-5 w-5 text-green-500" />
                        )}
                        {titleError && !isValidTitle && (
                          <ExclamationTriangleIcon className="absolute right-3 top-2 h-5 w-5 text-red-500" />
                        )}
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <div className="text-sm">
                          {titleError && (
                            <p className={`${isValidTitle ? 'text-amber-600' : 'text-red-600'}`}>
                              {titleError}
                            </p>
                          )}
                        </div>
                        <span className="text-xs text-gray-500">
                          {formData.title.length}/200
                        </span>
                      </div>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Start with &ldquo;What is it like to...&rdquo; for the best responses.
                    </p>
                  </div>

                  {/* Category Selection */}
                  <div className="sm:col-span-6">
                    <label htmlFor="category" className="block text-sm font-medium leading-6 text-gray-900">
                      Category
                    </label>
                    <div className="mt-2">
                      <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={(e) => handleInputChange('category', e.target.value)}
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                        required
                      >
                        <option value="">Select a category</option>
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.name} - {category.description}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Description */}
                  <div className="sm:col-span-6">
                    <label htmlFor="description" className="block text-sm font-medium leading-6 text-gray-900">
                      Additional Details
                    </label>
                    <div className="mt-2">
                      <textarea
                        id="description"
                        name="description"
                        rows={4}
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                        placeholder="Provide more context about what specifically you'd like to know..."
                        maxLength={1000}
                      />
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-sm text-gray-500">
                          Optional: Add more details to help people give you better answers.
                        </p>
                        <span className="text-xs text-gray-500">
                          {formData.description.length}/1000
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="sm:col-span-6">
                    <label htmlFor="tags" className="block text-sm font-medium leading-6 text-gray-900">
                      Tags
                    </label>
                    <div className="mt-2">
                      <input
                        type="text"
                        name="tags"
                        id="tags"
                        value={formData.tags}
                        onChange={(e) => handleInputChange('tags', e.target.value)}
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                        placeholder="remote-work, freelancing, career-change"
                      />
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Separate tags with commas to help people find your question.
                    </p>
                  </div>

                  {/* Similar Questions Warning */}
                  {showSimilar && similarQuestions.length > 0 && (
                    <div className="sm:col-span-6">
                      <div className="rounded-md bg-amber-50 border border-amber-200 p-4">
                        <div className="flex">
                          <ExclamationTriangleIcon className="h-5 w-5 text-amber-400 mt-0.5 flex-shrink-0" />
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-amber-800">
                              Similar questions found
                            </h3>
                            <div className="mt-2 text-sm text-amber-700">
                              <p className="mb-2">Consider checking these existing questions first:</p>
                              <ul className="space-y-1">
                                {similarQuestions.map((q) => (
                                  <li key={q.id} className="flex items-center justify-between">
                                    <span className="text-blue-600 hover:text-blue-800 cursor-pointer">
                                      {q.title}
                                    </span>
                                    <span className="text-xs text-gray-500">{q.votes} votes</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Anonymous Option */}
                  <div className="sm:col-span-6">
                    <div className="relative flex items-start">
                      <div className="flex h-6 items-center">
                        <input
                          id="anonymous"
                          name="anonymous"
                          type="checkbox"
                          checked={formData.isAnonymous}
                          onChange={(e) => handleInputChange('isAnonymous', e.target.checked)}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"
                        />
                      </div>
                      <div className="ml-3 text-sm leading-6">
                        <label htmlFor="anonymous" className="font-medium text-gray-900">
                          Post anonymously
                        </label>
                        <p className="text-gray-500">
                          Your username won&apos;t be shown with this question.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between border-t border-gray-900/10 px-4 py-4 sm:px-8">
                <div className="flex items-center gap-x-4">
                  <button
                    type="button"
                    className="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700"
                    onClick={() => router.back()}
                  >
                    Cancel
                  </button>
                  {(formData.title || formData.description) && (
                    <button
                      type="button"
                      onClick={clearDraft}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      Clear Draft
                    </button>
                  )}
                </div>
                <div className="flex items-center gap-x-3">
                  {(formData.title || formData.description) && (
                    <span className="text-xs text-gray-500">
                      Draft auto-saved
                    </span>
                  )}
                  <button
                    type="submit"
                    disabled={isSubmitting || !formData.title || !formData.category || (titleError && !isValidTitle)}
                    className="rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {isSubmitting && (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    )}
                    {isSubmitting ? 'Posting...' : 'Post Question'}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Tips */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start">
              <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">Tips for great questions</h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Be specific about what you want to know</li>
                    <li>Include relevant context or background</li>
                    <li>Ask about personal experiences, not facts</li>
                    <li>Consider adding tags to reach the right audience</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Question Examples */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center mb-3">
              <ChatBubbleLeftEllipsisIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900">Popular questions</h3>
            </div>
            <div className="space-y-2">
              {questionExamples.slice(0, 5).map((example, index) => (
                <button
                  key={index}
                  onClick={() => handleInputChange('title', example)}
                  className="block w-full text-left text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded p-2 transition-colors"
                >
                  {example}
                </button>
              ))}
            </div>
          </div>

          {/* Privacy Info */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <div className="flex items-start">
              <EyeSlashIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Your privacy</h3>
                <p className="mt-2 text-sm text-gray-600">
                  Questions are public by default. Check &ldquo;Post anonymously&rdquo; if you prefer not to show your username.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 