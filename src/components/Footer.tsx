import Link from 'next/link'
import { 
  EnvelopeIcon,
  ArrowRightIcon,
  HeartIcon
} from '@heroicons/react/24/outline'

const footerNavigation = {
  explore: [
    { name: 'Categories', href: '/categories' },
    { name: 'Trending', href: '/trending' },
    { name: 'AMA Sessions', href: '/ama' },
    { name: 'Top Contributors', href: '/contributors' },
  ],
  community: [
    { name: 'Guidelines', href: '/guidelines' },
    { name: 'Code of Conduct', href: '/code-of-conduct' },
    { name: 'Moderators', href: '/moderators' },
    { name: 'Help Center', href: '/help' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Contact', href: '/contact' },
    { name: 'Careers', href: '/careers' },
    { name: 'Press', href: '/press' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'DMCA', href: '/dmca' },
  ],
}

const socialLinks = [
  { name: 'Twitter', href: '#', icon: '🐦' },
  { name: 'Discord', href: '#', icon: '💬' },
  { name: 'GitHub', href: '#', icon: '🐙' },
  { name: 'LinkedIn', href: '#', icon: '💼' },
]

export function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="mx-auto max-w-7xl px-6 py-16 sm:py-20 lg:px-8">
        {/* Newsletter Subscription */}
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-6 xl:col-span-1">
            <Link href="/" className="text-2xl font-bold">
              <span className="text-blue-600">Whats</span>
              <span className="text-orange-500">it</span>
              <span className="text-blue-600">like.to</span>
            </Link>
            <p className="text-sm text-gray-600 max-w-xs">
              Share real experiences and get authentic insights from people who&apos;ve been there.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <Link
                  key={social.name}
                  href={social.href}
                  className="text-gray-400 hover:text-gray-500 transition-colors"
                >
                  <span className="sr-only">{social.name}</span>
                  <span className="text-xl">{social.icon}</span>
                </Link>
              ))}
            </div>
          </div>
          
          <div className="mt-16 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">
                  Stay in the loop
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Get the latest questions, trending topics, and expert insights delivered to your inbox.
                </p>
                <form className="mt-4 sm:flex sm:max-w-md">
                  <label htmlFor="email-address" className="sr-only">
                    Email address
                  </label>
                  <input
                    type="email"
                    name="email-address"
                    id="email-address"
                    autoComplete="email"
                    required
                    className="w-full min-w-0 appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 placeholder-gray-500 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="Enter your email"
                  />
                  <div className="mt-3 sm:ml-3 sm:mt-0 sm:flex-shrink-0">
                    <button
                      type="submit"
                      className="flex w-full items-center justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                    >
                      <span>Subscribe</span>
                      <ArrowRightIcon className="ml-2 h-4 w-4" />
                    </button>
                  </div>
                </form>
                <p className="mt-3 text-xs text-gray-500">
                  No spam, unsubscribe anytime. We respect your privacy.
                </p>
              </div>
              
              <div className="mt-8 md:mt-0">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">
                  Weekly Digest
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <EnvelopeIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Top Questions</p>
                      <p className="text-xs text-gray-500">Most popular questions of the week</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <span className="text-orange-500">🔥</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Trending Topics</p>
                      <p className="text-xs text-gray-500">What the community is discussing</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <span className="text-purple-500">✨</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Expert Insights</p>
                      <p className="text-xs text-gray-500">Curated answers from verified experts</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Navigation */}
        <div className="mt-16 border-t border-gray-200 pt-10">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                Explore
              </h3>
              <ul className="space-y-3">
                {footerNavigation.explore.map((item) => (
                  <li key={item.name}>
                    <Link href={item.href} className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                Community
              </h3>
              <ul className="space-y-3">
                {footerNavigation.community.map((item) => (
                  <li key={item.name}>
                    <Link href={item.href} className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                Company
              </h3>
              <ul className="space-y-3">
                {footerNavigation.company.map((item) => (
                  <li key={item.name}>
                    <Link href={item.href} className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                Legal
              </h3>
              <ul className="space-y-3">
                {footerNavigation.legal.map((item) => (
                  <li key={item.name}>
                    <Link href={item.href} className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-10 border-t border-gray-200 pt-8">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex space-x-6 md:order-2">
              <p className="text-xs text-gray-500">
                <Link href="/privacy" className="hover:text-gray-900">Privacy</Link>
              </p>
              <p className="text-xs text-gray-500">
                <Link href="/terms" className="hover:text-gray-900">Terms</Link>
              </p>
              <p className="text-xs text-gray-500">
                <Link href="/cookies" className="hover:text-gray-900">Cookies</Link>
              </p>
            </div>
            <div className="mt-8 md:order-1 md:mt-0">
              <p className="text-xs text-gray-500 flex items-center">
                &copy; 2024 Whatsitlike.to, Inc. Made with 
                <HeartIcon className="h-3 w-3 text-red-500 mx-1" />
                for curious minds.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
} 