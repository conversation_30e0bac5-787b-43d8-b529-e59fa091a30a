import Link from 'next/link'
import { formatTimeAgo, formatNumber } from '@/lib/utils'
import { 
  ChatBubbleLeftRightIcon, 
  ArrowUpIcon,
  UserIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'

interface QuestionCardProps {
  question: {
    id: string
    title: string
    description: string | null
    category: {
      name: string
      color: string
    }
    author: {
      username: string | null
      full_name: string | null
      is_verified: boolean
    } | null
    created_at: string
    upvotes: number
    downvotes: number
    answer_count: number
    is_anonymous: boolean
    tags: string[] | null
  }
  showAuthor?: boolean
  compact?: boolean
}

export function QuestionCard({ question, showAuthor = true, compact = false }: QuestionCardProps) {
  const netVotes = question.upvotes - question.downvotes

  return (
    <div className={`relative rounded-lg border border-gray-200 bg-white shadow-sm hover:shadow-md transition-shadow ${compact ? 'p-4' : 'p-6'}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* Category and timestamp */}
          <div className="flex items-center gap-3 mb-2">
            <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${question.category.color}`}>
              {question.category.name}
            </span>
            <span className="text-sm text-gray-500">
              {formatTimeAgo(question.created_at)}
            </span>
          </div>

          {/* Question title */}
          <h3 className={`font-semibold text-gray-900 hover:text-blue-600 ${compact ? 'text-base' : 'text-lg'}`}>
            <Link href={`/questions/${question.id}`} className="focus:outline-none">
              <span className="absolute inset-0" aria-hidden="true" />
              {question.title}
            </Link>
          </h3>

          {/* Description */}
          {question.description && !compact && (
            <p className="mt-2 text-gray-600 line-clamp-2">
              {question.description}
            </p>
          )}

          {/* Tags */}
          {question.tags && question.tags.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-1">
              {question.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-700"
                >
                  {tag}
                </span>
              ))}
              {question.tags.length > 3 && (
                <span className="inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-700">
                  +{question.tags.length - 3} more
                </span>
              )}
            </div>
          )}

          {/* Stats and author */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center gap-6 text-sm text-gray-500">
              <span className="flex items-center gap-1">
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
                {formatNumber(question.answer_count)} answers
              </span>
              <span className="flex items-center gap-1">
                <ArrowUpIcon className="h-4 w-4" />
                {formatNumber(netVotes)} votes
              </span>
            </div>

            {/* Author info */}
            {showAuthor && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                {question.is_anonymous ? (
                  <div className="flex items-center gap-1">
                    <EyeSlashIcon className="h-4 w-4" />
                    <span>Anonymous</span>
                  </div>
                ) : question.author ? (
                  <div className="flex items-center gap-2">
                    <UserIcon className="h-4 w-4" />
                    <span className="font-medium">
                      {question.author.full_name || question.author.username}
                    </span>
                    {question.author.is_verified && (
                      <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                        Verified
                      </span>
                    )}
                  </div>
                ) : (
                  <span>Unknown user</span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 