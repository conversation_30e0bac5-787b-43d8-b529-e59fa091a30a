# WhatIsItLike.to - Experience Everything

A modern social Q&A platform where people share real experiences about anything - from living in different countries to health conditions, career changes to lifestyle choices.

![WhatIsItLike.to](https://img.shields.io/badge/Next.js-000000?style=for-the-badge&logo=nextdotjs&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)
![Supabase](https://img.shields.io/badge/Supabase-181818?style=for-the-badge&logo=supabase&logoColor=white)

## 🚀 Features

### Core Functionality
- **Ask & Answer Questions**: "What is it like to..." format encourages personal experience sharing
- **Structured Categories**: Career, Countries, Health, Lifestyle, Education, Relationships, Technology, Finance
- **Voting System**: Upvote/downvote questions and answers to highlight quality content
- **Anonymous Posting**: Option to ask sensitive questions or share experiences anonymously
- **Credibility Tags**: Users can display their expertise (e.g., "Nurse for 10 years", "Lived in Japan 5+ years")
- **Rich Text Support**: Markdown support for detailed answers with formatting

### User Experience
- **Modern, Responsive UI**: Beautiful interface that works on all devices
- **Smart Search**: Find experiences and questions easily
- **Trending Content**: Discover popular questions and rising topics
- **User Profiles**: Build reputation and showcase expertise
- **Real-time Notifications**: Get notified of new answers and interactions

### Advanced Features
- **AMA Sessions**: Scheduled "Ask Me Anything" sessions with experts
- **AI Summarization**: Get condensed insights from multiple answers (coming soon)
- **Video Answers**: Optional video responses for more personal experiences
- **Tag System**: Organize and discover content with relevant tags
- **Community Moderation**: Report inappropriate content and maintain quality

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Authentication**: Supabase Auth with email/password and OAuth (Google)
- **Database**: PostgreSQL with Row Level Security
- **Icons**: Heroicons
- **Deployment**: Vercel (recommended)

## 🏗️ Project Structure

```
src/
├── app/                          # Next.js App Router
│   ├── auth/                     # Authentication pages
│   ├── ask/                      # Ask question page
│   ├── questions/                # Individual question pages
│   ├── categories/               # Category browsing
│   ├── profile/                  # User profiles
│   └── layout.tsx               # Root layout
├── components/                   # Reusable UI components
│   ├── AuthProvider.tsx         # Authentication context
│   ├── Navigation.tsx           # Main navigation
│   └── QuestionCard.tsx         # Question display component
├── lib/                         # Utilities and configurations
│   ├── supabase.ts             # Supabase client
│   ├── database.types.ts       # TypeScript database types
│   └── utils.ts                # Helper functions
└── styles/
    └── globals.css             # Global styles
```

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account

### 1. Clone the Repository
```bash
git clone <repository-url>
cd appCursor
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Set Up Supabase

1. **Create a Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Set Up Environment Variables**
   Create a `.env.local` file in the root directory:
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. **Database Setup**
   Run the following SQL in your Supabase SQL editor:

   ```sql
   -- Enable Row Level Security
   ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

   -- Create profiles table
   CREATE TABLE public.profiles (
     id UUID REFERENCES auth.users(id) PRIMARY KEY,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     username TEXT UNIQUE,
     full_name TEXT,
     bio TEXT,
     avatar_url TEXT,
     credibility_tags TEXT[],
     is_verified BOOLEAN DEFAULT FALSE
   );

   -- Create categories table
   CREATE TABLE public.categories (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     name TEXT NOT NULL,
     description TEXT,
     slug TEXT UNIQUE NOT NULL,
     color TEXT NOT NULL,
     icon TEXT
   );

   -- Create questions table
   CREATE TABLE public.questions (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     title TEXT NOT NULL,
     description TEXT,
     author_id UUID REFERENCES auth.users(id),
     category_id UUID REFERENCES public.categories(id),
     is_anonymous BOOLEAN DEFAULT FALSE,
     upvotes INTEGER DEFAULT 0,
     downvotes INTEGER DEFAULT 0,
     answer_count INTEGER DEFAULT 0,
     is_featured BOOLEAN DEFAULT FALSE,
     tags TEXT[]
   );

   -- Create answers table
   CREATE TABLE public.answers (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     content TEXT NOT NULL,
     question_id UUID REFERENCES public.questions(id),
     author_id UUID REFERENCES auth.users(id),
     is_anonymous BOOLEAN DEFAULT FALSE,
     upvotes INTEGER DEFAULT 0,
     downvotes INTEGER DEFAULT 0,
     is_best_answer BOOLEAN DEFAULT FALSE,
     credibility_info TEXT,
     video_url TEXT
   );

   -- Create votes table
   CREATE TABLE public.votes (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     user_id UUID REFERENCES auth.users(id),
     question_id UUID REFERENCES public.questions(id),
     answer_id UUID REFERENCES public.answers(id),
     vote_type TEXT CHECK (vote_type IN ('up', 'down'))
   );

   -- Create AMA sessions table
   CREATE TABLE public.ama_sessions (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     title TEXT NOT NULL,
     description TEXT NOT NULL,
     host_id UUID REFERENCES auth.users(id),
     category_id UUID REFERENCES public.categories(id),
     scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
     duration_minutes INTEGER NOT NULL,
     status TEXT CHECK (status IN ('upcoming', 'live', 'ended')) DEFAULT 'upcoming',
     max_questions INTEGER
   );

   -- Insert sample categories
   INSERT INTO public.categories (name, description, slug, color, icon) VALUES
   ('Career', 'Work, jobs, professional life', 'career', 'bg-blue-100 text-blue-800', '💼'),
   ('Countries', 'Living abroad, travel, cultures', 'countries', 'bg-purple-100 text-purple-800', '🌍'),
   ('Health', 'Medical conditions, wellness, mental health', 'health', 'bg-green-100 text-green-800', '🏥'),
   ('Lifestyle', 'Daily life, habits, personal choices', 'lifestyle', 'bg-orange-100 text-orange-800', '🏠'),
   ('Education', 'Schools, learning, academic life', 'education', 'bg-indigo-100 text-indigo-800', '🎓'),
   ('Relationships', 'Family, friends, romantic relationships', 'relationships', 'bg-pink-100 text-pink-800', '💝'),
   ('Technology', 'Tech industry, programming, digital life', 'technology', 'bg-gray-100 text-gray-800', '💻'),
   ('Finance', 'Money, investments, financial decisions', 'finance', 'bg-yellow-100 text-yellow-800', '💰');

   -- Enable RLS policies
   ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.questions ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.answers ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.ama_sessions ENABLE ROW LEVEL SECURITY;

   -- Create RLS policies (basic examples)
   CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles FOR SELECT USING (true);
   CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
   CREATE POLICY "Categories are viewable by everyone" ON public.categories FOR SELECT USING (true);
   CREATE POLICY "Questions are viewable by everyone" ON public.questions FOR SELECT USING (true);
   CREATE POLICY "Authenticated users can create questions" ON public.questions FOR INSERT WITH CHECK (auth.role() = 'authenticated');
   ```

### 4. Configure Authentication (Optional)

To enable Google OAuth:
1. Go to your Supabase project settings
2. Navigate to Authentication > Providers
3. Enable Google provider
4. Add your Google OAuth credentials

### 5. Run the Development Server
```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see your application.

## 🚀 Deployment

### Deploy to Vercel (Recommended)

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy on Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Add environment variables:
     - `NEXT_PUBLIC_SUPABASE_URL`
     - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - Deploy!

3. **Update Supabase Settings**
   - In your Supabase project, go to Authentication > URL Configuration
   - Add your Vercel deployment URL to the allowed origins

## 🧩 Key Components

### Authentication
- Built-in email/password authentication
- Google OAuth integration
- Protected routes and user sessions
- Profile management

### Question Management
- Rich question creation form
- Category selection and tagging
- Anonymous posting option
- Voting and engagement tracking

### User Experience
- Responsive design for all devices
- Modern UI with Tailwind CSS
- Real-time updates
- Comprehensive search and filtering

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🔮 Roadmap

- [ ] AI-powered answer summarization
- [ ] Video answer uploads
- [ ] Advanced search with filters
- [ ] Mobile app (React Native)
- [ ] Real-time chat for AMA sessions
- [ ] Reputation system and badges
- [ ] Content moderation tools
- [ ] Multi-language support
- [ ] Email notifications
- [ ] Analytics dashboard

## 📞 Support

If you have any questions or need help with setup, please:
- Open an issue on GitHub
- Join our community discussions
- Check the documentation

---

**Built with ❤️ using Next.js, TypeScript, and Supabase**
